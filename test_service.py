#!/usr/bin/env python3
"""
Test script to verify Profile Analytics service startup
"""
import sys
import os
import asyncio
import traceback

# Add project root to path
project_root = "/home/<USER>/Desktop/workspace/creatorverse_services/creatorverse_discovery_and_profile_analytics"
sys.path.insert(0, project_root)
os.chdir(project_root)

async def test_service_startup():
    """Test if the service can start up properly"""
    print("🧪 Testing CreatorVerse Profile Analytics Service...")
    print("=" * 60)
    
    try:
        print("1. Testing core imports...")
        from app.core.config import APP_CONFIG, get_database, get_discovery_redis
        print("   ✅ Core config imported successfully")
        
        print("2. Testing API imports...")
        from app.api.api_v1.api import api_router
        print("   ✅ API router imported successfully")
        
        print("3. Testing endpoint imports...")
        from app.api.api_v1.endpoints import health, filters, profile_analytics
        print("   ✅ All endpoint modules imported successfully")
        
        print("4. Testing main application...")
        from main import app
        print(f"   ✅ Main application imported successfully")
        print(f"   📋 Service: {app.title}")
        print(f"   📝 Description: {app.description}")
        
        print("5. Testing service configuration...")
        logger = APP_CONFIG.initialize_logger()
        print(f"   ✅ Logger initialized: {APP_CONFIG.service_name}")
        print(f"   🌍 Environment: {APP_CONFIG.environ}")
        
        print("6. Testing database connection...")
        db = get_database()
        print("   ✅ Database client initialized")
        
        print("7. Testing Redis connection...")
        redis_client = get_discovery_redis()
        print("   ✅ Redis client initialized")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("=" * 60)
        print("✨ CreatorVerse Profile Analytics Service is ready!")
        print("🚀 You can now start the service with: uvicorn main:app --port 8000")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        print("\n🔍 Full traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_service_startup())
    sys.exit(0 if success else 1)
