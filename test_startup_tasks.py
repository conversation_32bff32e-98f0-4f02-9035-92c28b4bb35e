#!/usr/bin/env python3
"""
Test script to validate startup tasks integration
"""
import sys
import asyncio
from pathlib import Path

# Add the app directory to Python path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

async def test_startup_tasks():
    """Test that startup tasks can be imported and executed without errors"""
    try:
        print("Testing startup tasks import and execution...")
        
        # Test import
        from app.utilities.startup_tasks import (
            initialize_all_startup_tasks_sync,
            initialize_all_startup_tasks_async
        )
        print("✅ Startup tasks imported successfully")
        
        # Test sync initialization
        result = initialize_all_startup_tasks_sync()
        print(f"✅ Sync startup tasks completed: {result}")
        
        # Test async initialization (this will fail without Redis/DB but we can catch it)
        try:
            await initialize_all_startup_tasks_async()
            print("✅ Async startup tasks completed successfully")
        except Exception as async_error:
            print(f"⚠️  Async startup tasks failed (expected without Redis/DB): {async_error}")
        
        print("✅ Startup tasks test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Startup tasks test failed: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_startup_tasks())
    sys.exit(0 if result else 1)
