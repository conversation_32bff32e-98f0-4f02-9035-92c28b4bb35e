"""
Filter Service - Manages saved filter sets and filter metadata
"""
import json
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.exc import IntegrityError

from app.core.config import APP_CONFIG, get_database, get_discovery_redis
from app.core.exceptions import Discovery<PERSON>rror, FilterValidationError, DatabaseError
from app.models.filter_models import SavedFilterSet
from app.schemas.filter_schemas import (
    SavedFilterSetCreate, SavedFilterSetUpdate, SavedFilterSetResponse,
    DiscoveryFilters, FilterMetadata, DEFAULT_FILTER_CATEGORIES
)
from app.core_helper.async_logger import with_trace_id


class FilterService:
    """Service for managing saved filter sets and filter metadata"""
    
    def __init__(self):
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self.redis_client = get_discovery_redis()
        self.db = get_database()
    
    @with_trace_id
    async def create_saved_filter_set(
        self,
        user_id: str,
        filter_data: SavedFilterSetCreate
    ) -> SavedFilterSetResponse:
        """Create a new saved filter set"""
        try:
            self.logger.info(f"Creating saved filter set '{filter_data.name}' for user {user_id}")
            
            # Validate filters
            await self._validate_filters(filter_data.filters)
            
            async with self.db.get_db() as session:
                # Check if user already has a filter set with this name
                existing = await session.execute(
                    select(SavedFilterSet).where(
                        and_(
                            SavedFilterSet.user_id == uuid.UUID(user_id),
                            SavedFilterSet.name == filter_data.name
                        )
                    )
                )
                
                if existing.scalar_one_or_none():
                    raise FilterValidationError(f"Filter set with name '{filter_data.name}' already exists")
                
                # Create new filter set
                saved_filter = SavedFilterSet(
                    user_id=uuid.UUID(user_id),
                    name=filter_data.name,
                    channel=filter_data.filters.platform if hasattr(filter_data.filters, 'platform') else 'instagram',
                    option_for='creator',  # Default to creator for now
                    filter_values=json.dumps(filter_data.filters.dict()),
                    is_shared=filter_data.is_public if hasattr(filter_data, 'is_public') else False
                )
                
                session.add(saved_filter)
                await session.commit()
                await session.refresh(saved_filter)
                
                # Invalidate user's filter cache
                await self._invalidate_user_filter_cache(user_id)
                
                # Update metrics
                await self._update_filter_metrics("created")
                
                self.logger.info(f"Created saved filter set: {saved_filter.id}")
                return await self._to_response_model(saved_filter)
                
        except IntegrityError as e:
            self.logger.error(f"Database integrity error: {e}")
            raise FilterValidationError("Filter set creation failed due to data constraints")
        except Exception as e:
            self.logger.error(f"Failed to create saved filter set: {e}")
            raise DiscoveryError(f"Failed to create saved filter set: {str(e)}")
    
    @with_trace_id
    async def get_saved_filter_sets(
        self,
        user_id: str,
        include_public: bool = True,
        favorites_only: bool = False,
        page: int = 1,
        page_size: int = 50
    ) -> List[SavedFilterSetResponse]:
        """Get saved filter sets for a user"""
        try:
            self.logger.info(f"Fetching saved filter sets for user {user_id}")
            
            # Try cache first
            cache_key = f"user_filters:{user_id}:{include_public}:{favorites_only}"
            cached_filters = await self._get_from_cache(cache_key)
            if cached_filters:
                self.logger.info("Serving saved filter sets from cache")
                return self._paginate_results(cached_filters, page, page_size)
            
            async with self.db.get_db() as session:
                # Build query conditions
                conditions = [SavedFilterSet.user_id == uuid.UUID(user_id)]
                
                if include_public:
                    # Include user's own filters and public filters from others
                    conditions = [
                        or_(
                            SavedFilterSet.user_id == uuid.UUID(user_id),
                            SavedFilterSet.is_shared == True
                        )
                    ]
                
                # Note: favorites_only is not supported since is_favorite doesn't exist in DB
                # if favorites_only:
                #     conditions.append(SavedFilterSet.is_favorite == True)

                # Execute query
                query = select(SavedFilterSet).where(and_(*conditions)).order_by(
                    desc(SavedFilterSet.usage_count),
                    desc(SavedFilterSet.updated_at)
                )
                
                result = await session.execute(query)
                filter_sets = result.scalars().all()
                
                # Convert to response models
                response_models = [await self._to_response_model(fs) for fs in filter_sets]
                
                # Cache the results
                await self._cache_user_filters(cache_key, response_models)
                
                self.logger.info(f"Retrieved {len(response_models)} saved filter sets")
                return self._paginate_results(response_models, page, page_size)
                
        except Exception as e:
            self.logger.error(f"Failed to get saved filter sets: {e}")
            raise DiscoveryError(f"Failed to retrieve saved filter sets: {str(e)}")
    
    @with_trace_id
    async def get_saved_filter_set(
        self,
        filter_set_id: str,
        user_id: Optional[str] = None
    ) -> SavedFilterSetResponse:
        """Get a specific saved filter set"""
        try:
            async with self.db.get_db() as session:
                query = select(SavedFilterSet).where(SavedFilterSet.id == uuid.UUID(filter_set_id))
                
                result = await session.execute(query)
                filter_set = result.scalar_one_or_none()
                
                if not filter_set:
                    raise FilterValidationError(f"Filter set not found: {filter_set_id}")
                
                # Check access permissions
                if user_id and str(filter_set.user_id) != user_id and not filter_set.is_shared:
                    raise FilterValidationError("Access denied to private filter set")
                
                # Update usage tracking
                if user_id and str(filter_set.user_id) == user_id:
                    await self._update_filter_usage(session, filter_set)
                
                return await self._to_response_model(filter_set)
                
        except FilterValidationError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to get saved filter set: {e}")
            raise DiscoveryError(f"Failed to retrieve saved filter set: {str(e)}")
    
    @with_trace_id
    async def update_saved_filter_set(
        self,
        filter_set_id: str,
        user_id: str,
        update_data: SavedFilterSetUpdate
    ) -> SavedFilterSetResponse:
        """Update a saved filter set"""
        try:
            async with self.db.get_db() as session:
                # Get existing filter set
                query = select(SavedFilterSet).where(
                    and_(
                        SavedFilterSet.id == uuid.UUID(filter_set_id),
                        SavedFilterSet.user_id == uuid.UUID(user_id)
                    )
                )
                
                result = await session.execute(query)
                filter_set = result.scalar_one_or_none()
                
                if not filter_set:
                    raise FilterValidationError("Filter set not found or access denied")
                
                # Update fields
                update_dict = update_data.dict(exclude_unset=True)
                
                if 'name' in update_dict:
                    # Check for name conflicts
                    existing = await session.execute(
                        select(SavedFilterSet).where(
                            and_(
                                SavedFilterSet.user_id == uuid.UUID(user_id),
                                SavedFilterSet.name == update_dict['name'],
                                SavedFilterSet.id != uuid.UUID(filter_set_id)
                            )
                        )
                    )
                    
                    if existing.scalar_one_or_none():
                        raise FilterValidationError(f"Filter set with name '{update_dict['name']}' already exists")
                    
                    filter_set.name = update_dict['name']
                
                if 'description' in update_dict:
                    filter_set.description = update_dict['description']
                
                if 'filters' in update_dict:
                    await self._validate_filters(update_dict['filters'])
                    filter_set.filters = json.dumps(update_dict['filters'].dict())
                
                if 'is_public' in update_dict:
                    filter_set.is_public = update_dict['is_public']
                
                if 'is_favorite' in update_dict:
                    filter_set.is_favorite = update_dict['is_favorite']
                
                filter_set.updated_at = datetime.utcnow()
                
                await session.commit()
                await session.refresh(filter_set)
                
                # Invalidate cache
                await self._invalidate_user_filter_cache(user_id)
                
                # Update metrics
                await self._update_filter_metrics("updated")
                
                self.logger.info(f"Updated saved filter set: {filter_set_id}")
                return await self._to_response_model(filter_set)
                
        except FilterValidationError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to update saved filter set: {e}")
            raise DiscoveryError(f"Failed to update saved filter set: {str(e)}")
    
    @with_trace_id
    async def delete_saved_filter_set(
        self,
        filter_set_id: str,
        user_id: str
    ) -> bool:
        """Delete a saved filter set"""
        try:
            async with self.db.get_db() as session:
                # Get existing filter set
                query = select(SavedFilterSet).where(
                    and_(
                        SavedFilterSet.id == uuid.UUID(filter_set_id),
                        SavedFilterSet.user_id == uuid.UUID(user_id)
                    )
                )
                
                result = await session.execute(query)
                filter_set = result.scalar_one_or_none()
                
                if not filter_set:
                    raise FilterValidationError("Filter set not found or access denied")
                
                await session.delete(filter_set)
                await session.commit()
                
                # Invalidate cache
                await self._invalidate_user_filter_cache(user_id)
                
                # Update metrics
                await self._update_filter_metrics("deleted")
                
                self.logger.info(f"Deleted saved filter set: {filter_set_id}")
                return True
                
        except FilterValidationError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to delete saved filter set: {e}")
            raise DiscoveryError(f"Failed to delete saved filter set: {str(e)}")
    
    @with_trace_id
    async def get_filter_metadata(self) -> FilterMetadata:
        """Get filter metadata for frontend"""
        try:
            # Try cache first
            cache_key = "discovery:filter_metadata"
            cached_metadata = await self._get_from_cache(cache_key)
            
            if cached_metadata:
                self.logger.info("Serving filter metadata from cache")
                return FilterMetadata(**cached_metadata)
            
            # Generate metadata
            metadata = FilterMetadata(
                categories=DEFAULT_FILTER_CATEGORIES,
                total_filters=sum(len(cat.filters) for cat in DEFAULT_FILTER_CATEGORIES),
                version="1.0"
            )
            
            # Cache the metadata
            await self.redis_client.set(
                cache_key,
                json.dumps(metadata.dict()),
                expire=APP_CONFIG.filter_cache_ttl
            )
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"Failed to get filter metadata: {e}")
            # Return default metadata on error
            return FilterMetadata(
                categories=DEFAULT_FILTER_CATEGORIES,
                total_filters=sum(len(cat.filters) for cat in DEFAULT_FILTER_CATEGORIES),
                version="1.0"
            )
    
    async def _validate_filters(self, filters: DiscoveryFilters):
        """Validate filter criteria"""
        try:
            # Basic validation - check for invalid combinations
            if filters.performance:
                perf = filters.performance
                
                # Validate range filters
                if perf.follower_count:
                    if (perf.follower_count.min_value is not None and 
                        perf.follower_count.min_value < 0):
                        raise FilterValidationError("Follower count minimum cannot be negative")
                
                if perf.engagement_rate:
                    if (perf.engagement_rate.max_value is not None and 
                        perf.engagement_rate.max_value > 100):
                        raise FilterValidationError("Engagement rate maximum cannot exceed 100%")
                
                # Add more validation logic as needed
                
        except FilterValidationError:
            raise
        except Exception as e:
            raise FilterValidationError(f"Filter validation failed: {str(e)}")
    
    async def _to_response_model(self, filter_set: SavedFilterSet) -> SavedFilterSetResponse:
        """Convert SavedFilterSet model to response model"""
        try:
            # Map database fields to frontend expected fields
            filters_dict = json.loads(filter_set.filter_values)
            filters = DiscoveryFilters(**filters_dict)

            return SavedFilterSetResponse(
                id=str(filter_set.id),
                name=filter_set.name,
                description=None,  # Not available in current DB schema
                filters=filters,
                is_public=filter_set.is_shared,  # Map is_shared to is_public
                is_favorite=False,  # Not available in current DB schema
                use_count=filter_set.usage_count,  # Map usage_count to use_count
                last_used_at=None,  # Not available in current DB schema
                created_at=filter_set.created_at.isoformat(),
                updated_at=filter_set.updated_at.isoformat()
            )
        except Exception as e:
            self.logger.error(f"Failed to convert filter set to response model: {e}")
            raise DiscoveryError("Failed to process filter set data")
    
    async def _update_filter_usage(self, session: AsyncSession, filter_set: SavedFilterSet):
        """Update filter set usage tracking"""
        try:
            filter_set.usage_count += 1
            # Note: last_used_at field doesn't exist in current DB schema
            await session.commit()
        except Exception as e:
            self.logger.warning(f"Failed to update filter usage: {e}")
    
    async def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get data from Redis cache"""
        try:
            cached_data = await self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            self.logger.warning(f"Cache retrieval failed: {e}")
        
        return None
    
    async def _cache_user_filters(self, cache_key: str, filter_sets: List[SavedFilterSetResponse]):
        """Cache user filter sets"""
        try:
            cache_data = [fs.dict() for fs in filter_sets]
            await self.redis_client.set(
                cache_key,
                json.dumps(cache_data),
                expire=APP_CONFIG.filter_cache_ttl
            )
        except Exception as e:
            self.logger.warning(f"Cache storage failed: {e}")
    
    async def _invalidate_user_filter_cache(self, user_id: str):
        """Invalidate all cache entries for a user's filters"""
        try:
            pattern = f"user_filters:{user_id}:*"
            await self.redis_client.delete_pattern(pattern)
        except Exception as e:
            self.logger.warning(f"Cache invalidation failed: {e}")
    
    def _paginate_results(self, results: List[Any], page: int, page_size: int) -> List[Any]:
        """Paginate results list"""
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        return results[start_idx:end_idx]
    
    async def _update_filter_metrics(self, action: str):
        """Update filter-related metrics"""
        try:
            metric_key = f"discovery:filter_metrics"
            await self.redis_client.hincrby(metric_key, f"filters_{action}", 1)
            await self.redis_client.hincrby(metric_key, "total_operations", 1)
        except Exception as e:
            self.logger.warning(f"Metrics update failed: {e}")
    
    @with_trace_id
    async def clone_filter_set(
        self,
        source_filter_id: str,
        user_id: str,
        new_name: str
    ) -> SavedFilterSetResponse:
        """Clone an existing filter set for a user"""
        try:
            # Get source filter set
            source_filter = await self.get_saved_filter_set(source_filter_id)
            
            # Create new filter set with cloned data
            clone_data = SavedFilterSetCreate(
                name=new_name,
                description=f"Cloned from: {source_filter.name}",
                filters=source_filter.filters,
                is_public=False,
                is_favorite=False
            )
            
            return await self.create_saved_filter_set(user_id, clone_data)
            
        except Exception as e:
            self.logger.error(f"Failed to clone filter set: {e}")
            raise DiscoveryError(f"Failed to clone filter set: {str(e)}")
