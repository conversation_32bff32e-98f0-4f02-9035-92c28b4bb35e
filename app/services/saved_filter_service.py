"""
Saved Filter Service for CreatorVerse Discovery & Profile Analytics
"""
import secrets
import string
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, update
from sqlalchemy.orm import selectinload

from app.core.config import get_database
from app.core.exceptions import FilterValidationError, DatabaseError
from app.models.filter_models import SavedFilterSet
from app.core_helper.async_logger import with_trace_id


class SavedFilterService:
    """Service for managing saved filter sets"""
    
    def __init__(self):
        from app.core.config import APP_CONFIG
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    
    @with_trace_id
    async def save_filter_set(
        self,
        user_id: UUID,
        name: str,
        filters: Dict[str, Any],
        is_shared: bool = False,
        tags: Optional[List[str]] = None
    ) -> SavedFilterSet:
        """Save a new filter set"""
        try:
            # Validate inputs
            if not name or not name.strip():
                raise FilterValidationError("Filter set name cannot be empty")
            
            if not filters:
                raise FilterValidationError("Filter configuration cannot be empty")
            
            # Generate share code if shared
            share_code = None
            if is_shared:
                share_code = self._generate_share_code()
            
            # Create filter set
            db_conn = get_database()
            async with db_conn.get_db() as db:
                # Check for duplicate names for this user
                existing = await db.execute(
                    select(SavedFilterSet).where(
                        and_(
                            SavedFilterSet.user_id == user_id,
                            SavedFilterSet.name == name.strip()
                        )
                    )
                )
                if existing.scalar_one_or_none():
                    raise FilterValidationError(f"Filter set with name '{name}' already exists")
                
                # Determine platform from filters
                platform = self._extract_platform_from_filters(filters)
                
                filter_set = SavedFilterSet(
                    name=name.strip(),
                    channel=platform,
                    option_for="creator",  # Default to creator filters
                    filter_values=filters,
                    user_id=user_id,
                    is_shared=is_shared,
                    share_code=share_code,
                    usage_count=0,
                    tags=tags or []
                )
                
                db.add(filter_set)
                await db.commit()
                await db.refresh(filter_set)
                
                self.logger.info(f"Saved filter set '{name}' for user {user_id}")
                return filter_set
                
        except FilterValidationError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to save filter set: {str(e)}")
            raise DatabaseError(f"Failed to save filter set: {str(e)}")
    
    def _generate_share_code(self, length: int = 8) -> str:
        """Generate a random share code"""
        alphabet = string.ascii_uppercase + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def _extract_platform_from_filters(self, filters: Dict[str, Any]) -> str:
        """Extract platform from filter configuration"""
        # Try to get platform from various possible locations
        if isinstance(filters, dict):
            # Direct platform field
            if "platform" in filters:
                return filters["platform"]
            
            # Platform in platform_filters
            if "platform_filters" in filters and isinstance(filters["platform_filters"], dict):
                platforms = filters["platform_filters"].get("platforms", [])
                if platforms:
                    return platforms[0] if isinstance(platforms[0], str) else platforms[0].get("value", "instagram")
            
            # Platform as top-level field
            if "platforms" in filters:
                platforms = filters["platforms"]
                if platforms:
                    return platforms[0] if isinstance(platforms[0], str) else platforms[0].get("value", "instagram")
        
        # Default to instagram
        return "instagram"
