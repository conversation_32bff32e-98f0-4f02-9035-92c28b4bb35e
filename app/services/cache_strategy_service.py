"""
Cache Strategy Service for CreatorVerse Discovery & Profile Analytics

This service implements intelligent caching strategies for filter sets, profile data,
and external API responses with appropriate TTL management.
"""

import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timedelta
from enum import Enum

from app.core.config import APP_CONFIG, get_database, get_discovery_redis
from app.core.exceptions import DiscoveryError
from app.core_helper.async_logger import with_trace_id
from app.models.profile_models import Profile, SavedFilterSet


class CacheType(Enum):
    """Cache type enumeration for different data types"""
    FILTER_SET = "filter_set"
    BASIC_METRICS = "basic_metrics"
    AUDIENCE_DATA = "audience_data"
    EXTERNAL_API = "external_api"
    PROFILE_ANALYTICS = "profile_analytics"


class CacheStrategyService:
    """
    Service for managing intelligent caching strategies across the discovery system
    """
    
    def __init__(self):
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self.redis = get_discovery_redis()
        self.db = get_database()
        
        # Cache TTL settings (in seconds)
        self.cache_ttl = {
            CacheType.FILTER_SET: 24 * 3600,  # 24 hours for filter sets
            CacheType.BASIC_METRICS: 24 * 3600,  # 24 hours for basic metrics
            CacheType.AUDIENCE_DATA: 7 * 24 * 3600,  # 7 days for audience data
            CacheType.EXTERNAL_API: 30 * 60,  # 30 minutes for external API responses
            CacheType.PROFILE_ANALYTICS: 6 * 3600,  # 6 hours for profile analytics
        }
        
        # Cache key prefixes
        self.cache_prefixes = {
            CacheType.FILTER_SET: "filter_set:",
            CacheType.BASIC_METRICS: "basic_metrics:",
            CacheType.AUDIENCE_DATA: "audience_data:",
            CacheType.EXTERNAL_API: "external_api:",
            CacheType.PROFILE_ANALYTICS: "profile_analytics:",
        }
    
    @with_trace_id
    async def get_cached_filter_results(
        self,
        cache_key: str,
        cache_type: CacheType = CacheType.FILTER_SET
    ) -> Optional[Dict[str, Any]]:
        """
        Get cached filter results with metadata
        
        Args:
            cache_key: Cache key to retrieve
            cache_type: Type of cache data
            
        Returns:
            Cached results with metadata or None if not found
        """
        try:
            full_cache_key = f"{self.cache_prefixes[cache_type]}{cache_key}"
            cached_data = await self.redis.get(full_cache_key)
            
            if cached_data:
                cache_obj = json.loads(cached_data)
                
                # Check if cache is still valid
                cached_at = datetime.fromisoformat(cache_obj.get('cached_at', ''))
                ttl = self.cache_ttl[cache_type]
                
                if datetime.utcnow() - cached_at < timedelta(seconds=ttl):
                    self.logger.info(f"Cache hit for {cache_type.value}: {cache_key}")
                    return cache_obj
                else:
                    # Cache expired, remove it
                    await self.redis.delete(full_cache_key)
                    self.logger.info(f"Cache expired for {cache_type.value}: {cache_key}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting cached filter results: {e}")
            return None
    
    @with_trace_id
    async def save_filter_results(
        self,
        cache_key: str,
        results: Dict[str, Any],
        cache_type: CacheType = CacheType.FILTER_SET,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Save filter results to cache with appropriate TTL
        
        Args:
            cache_key: Cache key for the results
            results: Results to cache
            cache_type: Type of cache data
            metadata: Additional metadata to store
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            full_cache_key = f"{self.cache_prefixes[cache_type]}{cache_key}"
            ttl = self.cache_ttl[cache_type]
            
            cache_data = {
                'results': results,
                'cached_at': datetime.utcnow().isoformat(),
                'cache_type': cache_type.value,
                'ttl': ttl,
                'metadata': metadata or {}
            }
            
            await self.redis.setex(
                full_cache_key,
                ttl,
                json.dumps(cache_data, default=str)
            )
            
            self.logger.info(f"Saved {cache_type.value} to cache: {cache_key} (TTL: {ttl}s)")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving filter results to cache: {e}")
            return False
    
    @with_trace_id
    async def check_profile_cache_validity(self, profile_id: str) -> Tuple[bool, Optional[datetime]]:
        """
        Check if a profile's cached data is still valid
        
        Args:
            profile_id: Profile ID to check
            
        Returns:
            Tuple of (is_valid, last_updated)
        """
        try:
            async with self.db.get_db() as session:
                from sqlalchemy import select
                
                result = await session.execute(
                    select(Profile.cache_expires_at, Profile.last_updated_external)
                    .where(Profile.id == profile_id)
                )
                profile_data = result.first()
                
                if not profile_data:
                    return False, None
                
                cache_expires_at, last_updated = profile_data
                
                if cache_expires_at and datetime.utcnow() < cache_expires_at:
                    return True, last_updated
                
                return False, last_updated
                
        except Exception as e:
            self.logger.error(f"Error checking profile cache validity: {e}")
            return False, None
    
    @with_trace_id
    async def update_profile_cache_metadata(
        self,
        profile_id: str,
        cache_expires_at: datetime,
        data_source: str = "phyllo"
    ) -> bool:
        """
        Update profile cache metadata in database
        
        Args:
            profile_id: Profile ID to update
            cache_expires_at: When the cache expires
            data_source: Source of the data
            
        Returns:
            True if updated successfully, False otherwise
        """
        try:
            async with self.db.get_db() as session:
                from sqlalchemy import update
                
                await session.execute(
                    update(Profile)
                    .where(Profile.id == profile_id)
                    .values(
                        cache_expires_at=cache_expires_at,
                        last_updated_external=datetime.utcnow(),
                        data_source=data_source
                    )
                )
                await session.commit()
                
                self.logger.info(f"Updated profile cache metadata: {profile_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating profile cache metadata: {e}")
            return False
    
    @with_trace_id
    async def get_saved_filter_sets(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get user's saved filter sets from database
        
        Args:
            user_id: User ID to get filter sets for
            
        Returns:
            List of saved filter sets
        """
        try:
            async with self.db.get_db() as session:
                from sqlalchemy import select
                
                result = await session.execute(
                    select(SavedFilterSet)
                    .where(SavedFilterSet.user_id == user_id)
                    .order_by(SavedFilterSet.last_used_at.desc())
                )
                filter_sets = result.scalars().all()
                
                return [
                    {
                        'id': str(fs.id),
                        'name': fs.name,
                        'description': fs.description,
                        'filters': json.loads(fs.filters),
                        'is_public': fs.is_public,
                        'is_favorite': fs.is_favorite,
                        'use_count': fs.use_count,
                        'last_used_at': fs.last_used_at.isoformat() if fs.last_used_at else None,
                        'created_at': fs.created_at.isoformat(),
                        'updated_at': fs.updated_at.isoformat()
                    }
                    for fs in filter_sets
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting saved filter sets: {e}")
            return []
    
    @with_trace_id
    async def save_filter_set_to_database(
        self,
        user_id: str,
        name: str,
        filters: Dict[str, Any],
        description: Optional[str] = None,
        is_public: bool = False
    ) -> Optional[str]:
        """
        Save a filter set to database for reuse
        
        Args:
            user_id: User ID saving the filter set
            name: Name for the filter set
            filters: Filter configuration
            description: Optional description
            is_public: Whether the filter set is public
            
        Returns:
            Filter set ID if saved successfully, None otherwise
        """
        try:
            async with self.db.get_db() as session:
                import uuid
                
                filter_set = SavedFilterSet(
                    id=uuid.uuid4(),
                    user_id=uuid.UUID(user_id),
                    name=name,
                    description=description,
                    filters=json.dumps(filters),
                    is_public=is_public,
                    is_favorite=False,
                    use_count=0
                )
                
                session.add(filter_set)
                await session.commit()
                await session.refresh(filter_set)
                
                self.logger.info(f"Saved filter set to database: {filter_set.id}")
                return str(filter_set.id)
                
        except Exception as e:
            self.logger.error(f"Error saving filter set to database: {e}")
            return None
    
    @with_trace_id
    async def invalidate_cache_pattern(self, pattern: str) -> int:
        """
        Invalidate cache entries matching a pattern
        
        Args:
            pattern: Redis pattern to match (e.g., "filter_set:instagram:*")
            
        Returns:
            Number of keys deleted
        """
        try:
            # Get all keys matching the pattern
            keys = await self.redis.keys(pattern)
            
            if keys:
                # Delete all matching keys
                deleted_count = await self.redis.delete(*keys)
                self.logger.info(f"Invalidated {deleted_count} cache entries matching pattern: {pattern}")
                return deleted_count
            
            return 0
            
        except Exception as e:
            self.logger.error(f"Error invalidating cache pattern: {e}")
            return 0
    
    @with_trace_id
    async def get_cache_statistics(self) -> Dict[str, Any]:
        """
        Get cache usage statistics
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            stats = {}
            
            for cache_type in CacheType:
                prefix = self.cache_prefixes[cache_type]
                keys = await self.redis.keys(f"{prefix}*")
                stats[cache_type.value] = {
                    'key_count': len(keys),
                    'ttl': self.cache_ttl[cache_type],
                    'prefix': prefix
                }
            
            # Get Redis info
            redis_info = await self.redis.info()
            stats['redis_info'] = {
                'used_memory': redis_info.get('used_memory_human', 'N/A'),
                'connected_clients': redis_info.get('connected_clients', 0),
                'total_commands_processed': redis_info.get('total_commands_processed', 0)
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting cache statistics: {e}")
            return {}
    
    async def cleanup_expired_cache(self) -> int:
        """
        Clean up expired cache entries
        
        Returns:
            Number of entries cleaned up
        """
        try:
            cleaned_count = 0
            
            for cache_type in CacheType:
                prefix = self.cache_prefixes[cache_type]
                keys = await self.redis.keys(f"{prefix}*")
                
                for key in keys:
                    ttl = await self.redis.ttl(key)
                    if ttl == -1:  # Key exists but has no expiration
                        # Set appropriate TTL
                        await self.redis.expire(key, self.cache_ttl[cache_type])
                    elif ttl == -2:  # Key doesn't exist
                        cleaned_count += 1
            
            self.logger.info(f"Cleaned up {cleaned_count} expired cache entries")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Error cleaning up expired cache: {e}")
            return 0
