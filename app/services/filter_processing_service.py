"""
Filter Processing Service for CreatorVerse Discovery & Profile Analytics

This service handles the conversion of frontend filter format to database-queryable format,
cache key generation, and filter validation for the discovery system.
"""

import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timedelta

from app.core.config import APP_CONFIG, get_database, get_discovery_redis
from app.core.exceptions import DiscoveryError
from app.core_helper.async_logger import with_trace_id
from app.schemas.filter_schemas import DiscoveryFilters, DemographicFilters, PerformanceFilters, ContentFilters, AudienceFilters, PlatformFilters


class FilterProcessingService:
    """
    Service for processing frontend filters and converting them to database queries
    """
    
    def __init__(self):
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self.redis = get_discovery_redis()
        self.db = get_database()
        
        # Cache TTL settings (in seconds)
        self.cache_ttl = {
            'filter_set': 24 * 3600,  # 24 hours for filter sets
            'basic_metrics': 24 * 3600,  # 24 hours for basic metrics
            'audience_data': 7 * 24 * 3600,  # 7 days for audience data
        }
    
    @with_trace_id
    async def process_frontend_filters(
        self,
        frontend_filters: Dict[str, Any],
        user_id: Optional[str] = None
    ) -> Tuple[DiscoveryFilters, str, bool]:
        """
        Process frontend filters and return database-queryable format with cache info
        
        Args:
            frontend_filters: Frontend filter format
            user_id: Optional user ID for personalized caching
            
        Returns:
            Tuple of (DiscoveryFilters, cache_key, cache_hit)
        """
        try:
            # Generate cache key for this filter set
            cache_key = self._generate_filter_cache_key(frontend_filters, user_id)
            
            # Check if we have cached results for this filter set
            cache_hit = await self._check_filter_cache(cache_key)
            
            # Convert frontend format to internal DiscoveryFilters
            discovery_filters = await self._convert_frontend_to_discovery_filters(frontend_filters)
            
            # Validate the converted filters
            await self._validate_discovery_filters(discovery_filters)
            
            self.logger.info(f"Processed frontend filters - cache_key: {cache_key}, cache_hit: {cache_hit}")
            
            return discovery_filters, cache_key, cache_hit
            
        except Exception as e:
            self.logger.error(f"Error processing frontend filters: {e}")
            raise DiscoveryError(f"Failed to process filters: {str(e)}")
    
    def _generate_filter_cache_key(self, frontend_filters: Dict[str, Any], user_id: Optional[str] = None) -> str:
        """
        Generate consistent cache key for filter sets
        
        Args:
            frontend_filters: Frontend filter format
            user_id: Optional user ID for personalized caching
            
        Returns:
            Consistent cache key string
        """
        try:
            # Extract key components for cache key
            filter_selections = frontend_filters.get('filterSelections', {})
            
            # Create a normalized representation for hashing
            cache_data = {
                'channel': filter_selections.get('channel', 'instagram'),
                'optionFor': filter_selections.get('optionFor', 'creator'),
                'filters': filter_selections.get('filters', {}),
                'sortBy': filter_selections.get('sortBy'),
                'sortOrder': filter_selections.get('sortOrder'),
                'searchQuery': frontend_filters.get('searchQuery', '').strip().lower(),
                'includeExternal': frontend_filters.get('includeExternal', True)
            }
            
            # Add user context if provided
            if user_id:
                cache_data['userId'] = user_id
            
            # Create deterministic JSON string
            cache_json = json.dumps(cache_data, sort_keys=True, separators=(',', ':'))
            
            # Generate MD5 hash for cache key
            cache_hash = hashlib.md5(cache_json.encode('utf-8')).hexdigest()
            
            # Create readable cache key
            channel = cache_data['channel']
            option_for = cache_data['optionFor']
            
            return f"filter_set:{channel}:{option_for}:{cache_hash}"
            
        except Exception as e:
            self.logger.error(f"Error generating cache key: {e}")
            # Fallback to timestamp-based key
            return f"filter_set:fallback:{int(datetime.utcnow().timestamp())}"
    
    async def _check_filter_cache(self, cache_key: str) -> bool:
        """
        Check if cached results exist for this filter set
        
        Args:
            cache_key: Cache key to check
            
        Returns:
            True if cache hit, False otherwise
        """
        try:
            cached_data = await self.redis.get(cache_key)
            return cached_data is not None
        except Exception as e:
            self.logger.warning(f"Error checking filter cache: {e}")
            return False
    
    async def _convert_frontend_to_discovery_filters(self, frontend_filters: Dict[str, Any]) -> DiscoveryFilters:
        """
        Convert frontend filter format to internal DiscoveryFilters format
        
        Args:
            frontend_filters: Frontend filter format
            
        Returns:
            DiscoveryFilters object
        """
        try:
            filter_selections = frontend_filters.get('filterSelections', {})
            filters = filter_selections.get('filters', {})
            
            # Initialize filter categories
            demographic = DemographicFilters()
            performance = PerformanceFilters()
            content = ContentFilters()
            audience = AudienceFilters()
            platform = PlatformFilters()
            
            # Convert each filter type
            for filter_name, value in filters.items():
                if value is None or value == "" or value == []:
                    continue
                
                # Demographic filters
                if filter_name == 'gender':
                    demographic.gender = value if isinstance(value, list) else [value]
                elif filter_name == 'age':
                    demographic.age_groups = self._convert_age_groups(value)
                elif filter_name == 'location':
                    demographic.locations = value if isinstance(value, list) else [value]
                elif filter_name == 'is_verified':
                    demographic.is_verified = value
                
                # Performance filters
                elif filter_name == 'follower_count':
                    if isinstance(value, dict):
                        performance.follower_count_min = value.get('min')
                        performance.follower_count_max = value.get('max')
                elif filter_name == 'engagement_rate':
                    if isinstance(value, dict):
                        performance.engagement_rate_min = value.get('min')
                        performance.engagement_rate_max = value.get('max')
                elif filter_name == 'average_likes':
                    if isinstance(value, dict):
                        performance.average_likes_min = value.get('min')
                        performance.average_likes_max = value.get('max')
                elif filter_name == 'average_views':
                    if isinstance(value, dict):
                        performance.average_views_min = value.get('min')
                        performance.average_views_max = value.get('max')
                
                # Content filters
                elif filter_name == 'category':
                    content.categories = value if isinstance(value, list) else [value]
                elif filter_name == 'hashtags':
                    content.hashtags = value if isinstance(value, list) else [value]
                elif filter_name == 'mentions':
                    content.mentions = value if isinstance(value, list) else [value]
                
                # Platform filters
                elif filter_name == 'platform':
                    platform.platforms = value if isinstance(value, list) else [value]
            
            # Set platform from channel if not in filters
            if not platform.platforms:
                channel = filter_selections.get('channel', 'instagram')
                platform.platforms = [channel]
            
            return DiscoveryFilters(
                demographic=demographic,
                performance=performance,
                content=content,
                audience=audience,
                platform=platform
            )
            
        except Exception as e:
            self.logger.error(f"Error converting frontend filters: {e}")
            raise DiscoveryError(f"Failed to convert filters: {str(e)}")
    
    def _convert_age_groups(self, age_values: Union[str, List[str]]) -> List[str]:
        """
        Convert frontend age group values to internal format
        
        Args:
            age_values: Frontend age group values
            
        Returns:
            List of internal age group values
        """
        age_mapping = {
            'teen': '13-17',
            'young_adult': '18-24',
            'adult': '25-34',
            'middle_aged': '35-44',
            'senior': '45-54',
            'elderly': '55+'
        }
        
        if isinstance(age_values, str):
            age_values = [age_values]
        
        converted = []
        for age in age_values:
            if age in age_mapping:
                converted.append(age_mapping[age])
            else:
                converted.append(age)  # Use as-is if no mapping found
        
        return converted
    
    async def _validate_discovery_filters(self, filters: DiscoveryFilters) -> None:
        """
        Validate the converted discovery filters
        
        Args:
            filters: DiscoveryFilters to validate
            
        Raises:
            DiscoveryError: If validation fails
        """
        try:
            # Validate performance ranges
            if filters.performance:
                perf = filters.performance
                
                # Follower count validation
                if perf.follower_count_min is not None and perf.follower_count_max is not None:
                    if perf.follower_count_min > perf.follower_count_max:
                        raise DiscoveryError("Minimum follower count cannot be greater than maximum")
                
                # Engagement rate validation
                if perf.engagement_rate_min is not None and perf.engagement_rate_max is not None:
                    if perf.engagement_rate_min > perf.engagement_rate_max:
                        raise DiscoveryError("Minimum engagement rate cannot be greater than maximum")
                    if perf.engagement_rate_max > 100:
                        raise DiscoveryError("Engagement rate cannot exceed 100%")
            
            # Validate platform filters
            if filters.platform and filters.platform.platforms:
                valid_platforms = ['instagram', 'youtube', 'tiktok', 'twitter', 'twitch']
                for platform in filters.platform.platforms:
                    if platform not in valid_platforms:
                        raise DiscoveryError(f"Invalid platform: {platform}")
            
            self.logger.debug("Discovery filters validation passed")
            
        except Exception as e:
            self.logger.error(f"Filter validation failed: {e}")
            raise
    
    async def save_filter_cache(self, cache_key: str, results: Dict[str, Any], ttl_type: str = 'basic_metrics') -> None:
        """
        Save filter results to cache
        
        Args:
            cache_key: Cache key for the results
            results: Results to cache
            ttl_type: Type of TTL to use ('basic_metrics', 'audience_data', 'filter_set')
        """
        try:
            ttl = self.cache_ttl.get(ttl_type, self.cache_ttl['basic_metrics'])
            
            cache_data = {
                'results': results,
                'cached_at': datetime.utcnow().isoformat(),
                'ttl_type': ttl_type
            }
            
            await self.redis.setex(
                cache_key,
                ttl,
                json.dumps(cache_data, default=str)
            )
            
            self.logger.info(f"Saved filter results to cache: {cache_key} (TTL: {ttl}s)")
            
        except Exception as e:
            self.logger.error(f"Error saving filter cache: {e}")
    
    async def get_filter_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Get filter results from cache
        
        Args:
            cache_key: Cache key to retrieve
            
        Returns:
            Cached results or None if not found
        """
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                cache_obj = json.loads(cached_data)
                return cache_obj.get('results')
            return None
        except Exception as e:
            self.logger.error(f"Error getting filter cache: {e}")
            return None
