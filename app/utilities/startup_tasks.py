"""
Startup tasks for CreatorVerse Discovery & Profile Analytics Backend
"""
import json
import httpx
from typing import Dict, Any

from app.core.config import APP_CONFIG, get_database, get_discovery_redis
from app.core_helper.async_logger import with_trace_id
from app.schemas.filter_schemas import DEFAULT_FILTER_CATEGORIES
# from app.services.filter_catalog_service import FilterCatalogService  # Removed complex dependency


def initialize_all_startup_tasks_sync():
    """Initialize all synchronous startup tasks"""
    try:
        # Ensure logger is initialized
        logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        logger.info("Starting initialization of synchronous startup tasks")

        # Add any synchronous startup tasks here if needed
        # For now, we'll just log that sync tasks are complete

        logger.info("Synchronous startup tasks completed successfully")
        return True
    except Exception as e:
        # Fallback to print if logger is not available
        error_msg = f"Failed to initialize synchronous startup tasks: {e}"
        if APP_CONFIG.logger:
            APP_CONFIG.logger.error(error_msg)
        else:
            print(error_msg)
        return False


@with_trace_id
async def initialize_all_startup_tasks_async():
    """Initialize all async startup tasks"""
    logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    logger.info("Starting async startup tasks initialization")

    try:
        # Initialize filter catalog cache
        await initialize_filter_catalog_cache()

        # Validate external API connectivity
        await validate_external_api_connectivity()

        # Pre-warm common cache keys
        await initialize_cache_warming()

        # Initialize discovery metrics
        await initialize_discovery_metrics()

        logger.info("All async startup tasks completed successfully")
    except Exception as e:
        logger.error(f"Failed to initialize async startup tasks: {e}", extra={"error": str(e)})
        raise


@with_trace_id
async def initialize_filter_catalog_cache():
    """
    Initialize filter catalog cache on application startup.
    This loads all filter categories, definitions, and metadata
    into Redis for faster access during filter operations.
    """
    logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    logger.info("Initializing filter catalog cache")

    try:
        # Get database and redis instances
        db_conn = get_database()
        redis_client = get_discovery_redis()
        
        # Ensure they are initialized (this should be done in main.py lifespan, but just in case)
        if not hasattr(db_conn, 'engine') or db_conn.engine is None:
            await db_conn.initialize()
        if not hasattr(redis_client, 'redis_client') or redis_client.redis_client is None:
            await redis_client.initialize()

        # Initialize filter catalog directly without complex service dependencies

        # Load default filter categories into cache
        cache_key = "discovery:filter_metadata"
        filter_metadata = {
            "categories": [category.dict() for category in DEFAULT_FILTER_CATEGORIES],
            "total_filters": sum(len(cat.filters) for cat in DEFAULT_FILTER_CATEGORIES),
            "version": "1.0",
            "last_updated": "startup"
        }

        await redis_client.set(
            cache_key,
            json.dumps(filter_metadata),
            expire=APP_CONFIG.filter_cache_ttl
        )

        # Initialize filter statistics
        await redis_client.hset(
            "discovery:filter_stats",
            "total_categories",
            len(DEFAULT_FILTER_CATEGORIES)
        )
        await redis_client.hset(
            "discovery:filter_stats",
            "total_filters",
            filter_metadata["total_filters"]
        )

        logger.info(f"Filter catalog cache initialized with {len(DEFAULT_FILTER_CATEGORIES)} categories and {filter_metadata['total_filters']} filters")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize filter catalog cache: {str(e)}", extra={"error": str(e)})
        # Log error but don't prevent application startup
        # The service will fall back to generating metadata on-demand
        return False


@with_trace_id
async def validate_external_api_connectivity():
    """
    Validate connectivity to external APIs (Phyllo) on startup.
    This ensures that external dependencies are available and properly configured.
    """
    logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    logger.info("Validating external API connectivity")

    try:
        # Test Phyllo API connectivity
        phyllo_base_url = APP_CONFIG.phyllo_base_url
        
        if phyllo_base_url:
            async with httpx.AsyncClient(timeout=10.0) as client:
                try:
                    response = await client.get(f"{phyllo_base_url}/health")
                    if response.status_code == 200:
                        logger.info(f"Phyllo API connectivity verified: {phyllo_base_url}")
                    else:
                        logger.warning(f"Phyllo API responded with status {response.status_code}")
                except httpx.TimeoutException:
                    logger.warning("Phyllo API connectivity check timed out")
                except httpx.ConnectError:
                    logger.warning(f"Failed to connect to Phyllo API at {phyllo_base_url}")
                except Exception as api_error:
                    logger.warning(f"Phyllo API connectivity check failed: {api_error}")
        else:
            logger.warning("Phyllo base URL not configured")

        logger.info("External API connectivity validation completed")
        return True
    except Exception as e:
        logger.error(f"Failed to validate external API connectivity: {str(e)}", extra={"error": str(e)})
        # Log error but don't prevent application startup
        # The service will handle API connectivity issues during runtime
        return False


@with_trace_id
async def initialize_cache_warming():
    """
    Pre-warm commonly used cache keys to improve initial response times.
    This includes setting up cache structures and default values.
    """
    logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    logger.info("Initializing cache warming")

    try:
        redis_client = get_discovery_redis()

        # Ensure redis is initialized
        if not hasattr(redis_client, 'redis_client') or redis_client.redis_client is None:
            await redis_client.initialize()

        # Pre-warm common cache prefixes
        cache_prefixes = [
            "profile_analytics:basic:",
            "profile_analytics:demographics:",
            "profile_analytics:insights:",
            "profile_analytics:sponsored:",
            "profile_analytics:similar:",
            "user_filters:",
            "discovery:filter_"
        ]

        # Initialize cache prefix tracking
        for prefix in cache_prefixes:
            await redis_client.hset(
                "discovery:cache_prefixes",
                prefix,
                0  # Initialize with 0 entries
            )

        # Set up cache TTL defaults in a hash for easy reference
        cache_ttls = {
            "basic_metrics": str(getattr(APP_CONFIG, 'profile_cache_ttl', 3600)),
            "audience_demographics": str(7 * 24 * 3600),  # 7 days
            "audience_insights": str(7 * 24 * 3600),  # 7 days
            "sponsored_content": str(24 * 3600),  # 24 hours
            "similar_creators": str(3 * 24 * 3600),  # 3 days
            "filter_cache": str(getattr(APP_CONFIG, 'filter_cache_ttl', 86400)),
            "external_api": str(getattr(APP_CONFIG, 'external_api_cache_ttl', 1800))
        }

        for cache_type, ttl in cache_ttls.items():
            await redis_client.hset("discovery:cache_ttls", cache_type, ttl)

        logger.info(f"Cache warming initialized with {len(cache_prefixes)} prefixes and {len(cache_ttls)} TTL configurations")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize cache warming: {str(e)}", extra={"error": str(e)}")
        # Log error but don't prevent application startup
        return False


@with_trace_id
async def initialize_discovery_metrics():
    """
    Initialize discovery service metrics and counters.
    This sets up metric tracking for monitoring service performance.
    """
    logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    logger.info("Initializing discovery metrics")

    try:
        redis_client = get_discovery_redis()

        # Ensure redis is initialized
        if not hasattr(redis_client, 'redis_client') or redis_client.redis_client is None:
            await redis_client.initialize()

        # Initialize metric counters
        metrics_to_initialize = {
            "total_filter_requests": 0,
            "total_profile_requests": 0,
            "total_external_api_calls": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "filters_created": 0,
            "filters_updated": 0,
            "filters_deleted": 0,
            "successful_requests": 0,
            "failed_requests": 0
        }

        for metric_name, initial_value in metrics_to_initialize.items():
            await redis_client.hset("discovery:metrics", metric_name, initial_value)

        # Initialize service status
        import time
        await redis_client.hset("discovery:service_status", "startup_time", str(int(time.time())))
        await redis_client.hset("discovery:service_status", "status", "healthy")
        await redis_client.hset("discovery:service_status", "version", "1.0.0")

        # Set up metric aggregation periods (for future use)
        aggregation_periods = ["hourly", "daily", "weekly", "monthly"]
        for period in aggregation_periods:
            await redis_client.hset("discovery:metric_periods", period, "enabled")

        logger.info(f"Discovery metrics initialized with {len(metrics_to_initialize)} counters")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize discovery metrics: {str(e)}", extra={"error": str(e)}")
        # Log error but don't prevent application startup
        return False


@with_trace_id
async def cleanup_startup_tasks():
    """
    Cleanup function for startup tasks (called during shutdown if needed).
    This is available for future use if cleanup is required.
    """
    logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
    logger.info("Cleanup startup tasks (if any)")

    try:
        # For now, no specific cleanup needed
        # This function is available for future cleanup requirements
        
        logger.info("Startup tasks cleanup completed")
        return True
    except Exception as e:
        logger.error(f"Failed to cleanup startup tasks: {str(e)}", extra={"error": str(e)}")
        return False
