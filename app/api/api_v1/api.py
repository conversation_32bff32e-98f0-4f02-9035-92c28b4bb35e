"""
API router for CreatorVerse Discovery & Analytics Backend
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import filters, health, profile_analytics
from app.api.v1 import filter_catalog

# Create main API router
api_router = APIRouter()

api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(filters.router, prefix="/filters", tags=["filters"])
api_router.include_router(filter_catalog.router, prefix="/filter-catalog", tags=["filter-catalog"])
api_router.include_router(profile_analytics.router, prefix="/profile-analytics", tags=["profile-analytics"])
