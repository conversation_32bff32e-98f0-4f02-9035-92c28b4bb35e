"""
Health check endpoints for CreatorVerse Discovery & Analytics API
"""
from fastapi import APIRouter, Depends
from typing import Dict, Any

from app.utilities.response_handler import StandardResponse, StandardResponseModel
# Removed dependency on startup_tasks - implementing health checks directly
from app.core.config import APP_CONFIG

router = APIRouter()


@router.get("/", response_model=StandardResponseModel[dict])
async def health_check():
    """Basic health check endpoint"""
    return StandardResponse.success(
        data={
            "status": "healthy",
            "service": "CreatorVerse Profile Analytics",
            "environment": APP_CONFIG.environ
        },
        message="Service is healthy"
    )


@router.get("/detailed", response_model=StandardResponseModel[dict])
async def detailed_health_check():
    """Detailed health check with component status"""
    # Simple health checks without dependency on removed startup_tasks
    db_healthy = await _check_database_health()
    redis_healthy = await _check_redis_health()
    
    overall_healthy = db_healthy and redis_healthy
    status_code = 200 if overall_healthy else 503
    message = "All systems operational" if overall_healthy else "Some systems degraded"
    
    return StandardResponse.success(
        data={
            "status": "healthy" if overall_healthy else "degraded",
            "service": "CreatorVerse Profile Analytics",
            "environment": APP_CONFIG.environ,
            "components": {
                "database": db_healthy,
                "redis": redis_healthy,
                "overall": overall_healthy
            }
        },
        message=message
    )


@router.get("/metrics")
async def service_metrics():
    """Get basic service metrics"""
    try:
        from app.core.config import get_discovery_redis
        redis_client = get_discovery_redis()
        
        # Get metrics from Redis (updated for profile analytics)
        metrics = await redis_client.hgetall("profile_analytics:metrics")
        
        if not metrics:
            metrics = {
                "total_requests": "0",
                "cache_hits": "0",
                "external_api_calls": "0"
            }
        
        # Convert to integers
        parsed_metrics = {key: int(value) for key, value in metrics.items()}
        
        return StandardResponse.success(
            data={
                "service_metrics": parsed_metrics,
                "service": "CreatorVerse Profile Analytics",
                "environment": APP_CONFIG.environ
            },
            message="Metrics retrieved successfully"
        )
        
    except Exception as e:
        return StandardResponse.error(
            message=f"Failed to retrieve metrics: {str(e)}",
            error_code="METRICS_ERROR"
        )


# Helper functions for health checks
async def _check_database_health() -> bool:
    """Check database health"""
    try:
        from app.core.config import get_database
        db = get_database()
        async with db.get_db() as session:
            # Simple query to test connection
            await session.execute("SELECT 1")
        return True
    except Exception:
        return False


async def _check_redis_health() -> bool:
    """Check Redis health"""
    try:
        from app.core.config import get_discovery_redis
        redis_client = get_discovery_redis()
        await redis_client.ping()
        return True
    except Exception:
        return False
