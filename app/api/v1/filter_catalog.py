"""
Filter Catalog API endpoints for CreatorVerse Profile Analytics

Provides endpoints for retrieving filter configurations in the exact format
expected by the frontend components.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Query, HTTPException, Depends
from fastapi.responses import J<PERSON>NResponse

from app.services.filter_catalog_service import filter_catalog_service
from app.models.filter_models import PlatformEnum, OptionForTypeEnum
from app.core.exceptions import CreatorVerseError
from app.core_helper.async_logger import with_trace_id
from app.core.config import APP_CONFIG


router = APIRouter(tags=["filter-catalog"])


@router.get("/filters")
@with_trace_id
async def get_filters(
    channel: PlatformEnum = Query(..., description="Platform type"),
    option_for: OptionForTypeEnum = Query(..., description="Target type (creator or audience)"),
    use_cache: bool = Query(True, description="Whether to use cache"),
    include_inactive: bool = Query(False, description="Include inactive filters")
) -> JSONResponse:
    """
    Get filter configurations for the frontend in the exact required format.
    
    This endpoint returns filter data structured exactly as expected by the
    frontend components, including proper grouping, multilevel options, and
    all necessary UI configuration.
    
    **Response Format:**
    ```json
    [
        {
            "optionName": "Demography & Identity",
            "optionFor": "creator",
            "channel": "instagram", 
            "filters": [
                {
                    "name": "gender",
                    "type": "radio-button",
                    "icon": "gender-icon",
                    "minmax": false,
                    "enterValue": false,
                    "placeholder": "Select Gender",
                    "options": [...]
                }
            ]
        }
    ]
    ```
    """
    try:
        # Get filters from service
        filters = await filter_catalog_service.get_filters_for_frontend(
            channel=channel,
            option_for=option_for,
            use_cache=use_cache
        )
        
        # Prepare response metadata
        response_data = {
            "success": True,
            "data": filters,
            "meta": {
                "channel": channel.value,
                "option_for": option_for.value,
                "total_groups": len(filters),
                "total_filters": sum(len(group["filters"]) for group in filters),
                "cache_used": use_cache,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
        return JSONResponse(
            content=response_data,
            status_code=200,
            headers={
                "Cache-Control": "public, max-age=3600" if use_cache else "no-cache",
                "X-Filter-Version": "1.0"
            }
        )
        
    except CreatorVerseError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        APP_CONFIG.logger.error(f"Unexpected error in get_filters: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/filters/all")
@with_trace_id
async def get_all_filters(
    use_cache: bool = Query(True, description="Whether to use cache")
) -> JSONResponse:
    """
    Get all filter configurations for all platforms and targets.
    
    This endpoint returns a comprehensive view of all available filters
    organized by platform and target type.
    """
    try:
        all_filters = {}
        
        # Get filters for all combinations
        for channel in PlatformEnum:
            all_filters[channel.value] = {}
            for option_for in OptionForTypeEnum:
                filters = await filter_catalog_service.get_filters_for_frontend(
                    channel=channel,
                    option_for=option_for,
                    use_cache=use_cache
                )
                all_filters[channel.value][option_for.value] = filters
        
        response_data = {
            "success": True,
            "data": all_filters,
            "meta": {
                "platforms": [p.value for p in PlatformEnum],
                "targets": [t.value for t in OptionForTypeEnum],
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
        return JSONResponse(content=response_data, status_code=200)
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Error in get_all_filters: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/filters/cache/invalidate")
@with_trace_id
async def invalidate_filter_cache(
    channel: Optional[PlatformEnum] = None,
    option_for: Optional[OptionForTypeEnum] = None
) -> JSONResponse:
    """
    Invalidate filter cache for specific or all combinations.
    
    Use this endpoint after updating filter configurations to ensure
    the latest data is served to clients.
    """
    try:
        await filter_catalog_service.invalidate_cache(
            channel=channel,
            option_for=option_for
        )
        
        scope = "all"
        if channel and option_for:
            scope = f"{channel.value}:{option_for.value}"
        elif channel:
            scope = f"{channel.value}:*"
        elif option_for:
            scope = f"*:{option_for.value}"
        
        return JSONResponse(
            content={
                "success": True,
                "message": f"Cache invalidated for scope: {scope}",
                "timestamp": datetime.utcnow().isoformat()
            },
            status_code=200
        )
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Error invalidating cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/filters/statistics")
@with_trace_id
async def get_filter_statistics() -> JSONResponse:
    """
    Get statistics about the filter catalog.
    
    Returns information about the number of filter groups and definitions
    organized by platform and target type.
    """
    try:
        stats = await filter_catalog_service.get_filter_statistics()
        
        return JSONResponse(
            content={
                "success": True,
                "data": stats,
                "timestamp": datetime.utcnow().isoformat()
            },
            status_code=200
        )
        
    except CreatorVerseError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        APP_CONFIG.logger.error(f"Error getting statistics: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/filters/validate")
@with_trace_id
async def validate_filter_structure(
    channel: PlatformEnum = Query(..., description="Platform type"),
    option_for: OptionForTypeEnum = Query(..., description="Target type")
) -> JSONResponse:
    """
    Validate filter structure for a specific platform and target.
    
    This endpoint checks if the filter configuration is valid and complete
    for the specified platform and target combination.
    """
    try:
        filters = await filter_catalog_service.get_filters_for_frontend(
            channel=channel,
            option_for=option_for,
            use_cache=False
        )
        
        # Validation checks
        validation_results = {
            "valid": True,
            "issues": [],
            "warnings": [],
            "summary": {
                "total_groups": len(filters),
                "total_filters": 0,
                "filter_types": set(),
                "missing_icons": 0,
                "missing_placeholders": 0
            }
        }
        
        for group in filters:
            group_filters = group.get("filters", [])
            validation_results["summary"]["total_filters"] += len(group_filters)
            
            for filter_def in group_filters:
                # Check filter type
                filter_type = filter_def.get("type")
                if filter_type:
                    validation_results["summary"]["filter_types"].add(filter_type)
                
                # Check for missing icons
                if not filter_def.get("icon"):
                    validation_results["summary"]["missing_icons"] += 1
                    validation_results["warnings"].append(
                        f"Filter '{filter_def.get('name')}' in group '{group.get('optionName')}' has no icon"
                    )
                
                # Check for missing placeholders on input fields
                if filter_def.get("enterValue") and not filter_def.get("placeholder"):
                    validation_results["summary"]["missing_placeholders"] += 1
                    validation_results["warnings"].append(
                        f"Input filter '{filter_def.get('name')}' has no placeholder text"
                    )
                
                # Validate multilevel options structure
                if filter_type == "multilevel-checkbox":
                    options = filter_def.get("options", [])
                    for option in options:
                        if not all(key in option for key in ["subOptionName", "subOptionType", "subOptions"]):
                            validation_results["issues"].append(
                                f"Multilevel filter '{filter_def.get('name')}' has invalid option structure"
                            )
                            validation_results["valid"] = False
        
        # Convert set to list for JSON serialization
        validation_results["summary"]["filter_types"] = list(validation_results["summary"]["filter_types"])
        
        return JSONResponse(
            content={
                "success": True,
                "data": validation_results,
                "timestamp": datetime.utcnow().isoformat()
            },
            status_code=200
        )
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Error validating filters: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
