#!/bin/bash

# Test script to check if the application starts without errors
cd "/home/<USER>/Desktop/workspace/creatorverse_services/creatorverse_discovery_and_profile_analytics"

echo "Testing application startup..."
python -c "
import sys
sys.path.insert(0, '.')

try:
    print('Testing imports...')
    from main import app
    print('✅ Main application imported successfully!')
    print(f'Service: {app.title}')
    print(f'Description: {app.description}')
    
    # Test if API router is working
    from app.api.api_v1.api import api_router
    print('✅ API router imported successfully!')
    
    # Test remaining endpoints
    from app.api.api_v1.endpoints import health, filters, profile_analytics
    print('✅ All endpoint modules imported successfully!')
    
    print('\\n🎉 All tests passed! Discovery module removal was successful!')
    
except Exception as e:
    print(f'❌ Error: {str(e)}')
    import traceback
    traceback.print_exc()
"
