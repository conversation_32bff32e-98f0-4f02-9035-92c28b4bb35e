# CreatorVerse Filter Discovery System
# Clean Dependencies for Filter-Based Creator Discovery Only

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database (PostgreSQL for filter_catalog schema)
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0

# Redis (for filter result caching)
redis[hiredis]==5.0.1

# HTTP Client (for Phyllo API integration)
httpx==0.25.2

# Data Processing (minimal for filter operations)
python-dateutil==2.8.2

# Environment Management
# python-dotenv==1.0.0  # Removed - using only appsettings.json as per guidelines

# Time Handling
pytz==2023.3

# Utilities
click==8.1.7

# Rate Limiting (for API protection)
slowapi==0.1.9

# Production Server
gunicorn==21.2.0

# Async Utilities
async-timeout==4.0.3

# Retry Logic (for external API calls)
tenacity==8.2.3

# JSON Processing (for filter data)
orjson==3.9.10

# Development & Testing (Optional)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0

# Code Quality (Optional)
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Mock Services for Testing
responses==0.24.1
faker==20.1.0

# REMOVED ANALYTICS DEPENDENCIES:
# - confluent-kafka (no message streaming)
# - alembic (no migrations)
# - pandas, numpy (no data analytics)
# - prometheus-client (no monitoring)
# - structlog, sentry-sdk (simplified logging)
# - apscheduler (no background jobs)
# - psutil (no system monitoring)
# - python-jose, passlib, cryptography (no authentication)
# - aiosmtplib, jinja2 (no email/templating)
# - google-api-python-client (no direct Google APIs)
# - openpyxl (no data export)
# - aiofiles (no file operations)
