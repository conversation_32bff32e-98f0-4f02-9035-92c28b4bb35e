"""
Creator Discovery API endpoints
"""
from fastapi import APIRouter, HTTPException, Query, Body
from typing import Optional, List

from app.services.creator_discovery_service import CreatorDiscoveryService
from app.schemas.discovery_schemas import (
    DiscoveryRequest, SavedFilterDiscoveryRequest, ProfileAnalyticsRequest,
    BatchDiscoveryRequest, ViewMode, DataSource, SortField, SortOrder
)
from app.schemas.filter_schemas import DiscoveryFilters
from app.utilities.response_handler import StandardResponse, StandardResponseModel, create_discovery_response
from app.core.exceptions import DiscoveryError, FilterValidationError, ProfileNotFoundError

router = APIRouter()
discovery_service = CreatorDiscoveryService()


@router.post("/search", response_model=StandardResponseModel[dict])
async def discover_creators(
    request: DiscoveryRequest = Body(...),
    user_id: Optional[str] = Query(None, description="User ID for tracking (optional)")
):
    """
    Discover creators based on filter criteria
    
    This endpoint allows brands to search for creators using various filters:
    - Demographic filters (gender, age, location, etc.)
    - Performance filters (follower count, engagement rate, etc.)
    - Content filters (categories, interests, etc.)
    - Audience filters (audience demographics)
    
    Returns paginated results with metadata about the query performance.
    """
    try:
        profiles, meta = await discovery_service.discover_creators(request, user_id)
        
        return StandardResponse.success(
            data={
                "profiles": profiles,
                "meta": meta.dict()
            },
            message=f"Found {meta.total_count} creators matching your criteria"
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search/saved-filter", response_model=StandardResponseModel[dict])
async def discover_creators_with_saved_filter(
    request: SavedFilterDiscoveryRequest = Body(...),
    user_id: Optional[str] = Query(None, description="User ID for access control")
):
    """
    Discover creators using a saved filter set
    
    This endpoint allows users to run discovery using previously saved filter sets,
    with optional additional filters to refine the results.
    """
    try:
        # Get the saved filter and merge with additional filters
        from app.services.filter_service import FilterService
        filter_service = FilterService()
        
        saved_filter = await filter_service.get_saved_filter_set(
            request.saved_filter_id, user_id
        )
        
        # Create discovery request
        discovery_request = DiscoveryRequest(
            filters=saved_filter.filters,
            sort_by=request.sort_by,
            view_mode=request.view_mode,
            page=request.page,
            page_size=request.page_size
        )
        
        # Merge additional filters if provided
        if request.additional_filters:
            discovery_request.filters = _merge_filters(
                discovery_request.filters, 
                request.additional_filters
            )
        
        profiles, meta = await discovery_service.discover_creators(discovery_request, user_id)
        
        return StandardResponse.success(
            data={
                "profiles": profiles,
                "meta": meta.dict(),
                "saved_filter": {
                    "id": saved_filter.id,
                    "name": saved_filter.name
                }
            },
            message=f"Found {meta.total_count} creators using saved filter '{saved_filter.name}'"
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/profile/{profile_id}", response_model=StandardResponseModel[dict])
async def get_profile_analytics(
    profile_id: str,
    include_audience: bool = Query(True, description="Include audience demographics"),
    include_content_analysis: bool = Query(True, description="Include content analysis"),
    refresh_external: bool = Query(False, description="Force refresh from external API"),
    date_range: Optional[str] = Query(None, description="Date range (30d, 90d, 1y)")
):
    """
    Get detailed profile analytics for a specific creator
    
    Returns comprehensive profile data including:
    - Basic profile information
    - Performance metrics and trends
    - Audience demographics
    - Content analysis
    - Competitor comparisons
    """
    try:
        # This would be implemented to get detailed profile data
        # For now, return a placeholder response
        
        return StandardResponse.success(
            data={
                "profile_id": profile_id,
                "message": "Profile analytics endpoint - implementation pending"
            },
            message="Profile analytics retrieved successfully"
        )
        
    except ProfileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch-search", response_model=StandardResponseModel[dict])
async def batch_discover_creators(
    request: BatchDiscoveryRequest = Body(...),
    user_id: Optional[str] = Query(None, description="User ID for tracking")
):
    """
    Batch discovery with multiple filter sets
    
    Allows running multiple discovery queries in a single request,
    useful for comparing different filter criteria.
    """
    try:
        batch_results = []
        
        for i, filter_set in enumerate(request.filter_sets):
            discovery_request = DiscoveryRequest(
                filters=filter_set,
                view_mode=request.view_mode,
                page=1,
                page_size=request.max_results_per_set
            )
            
            profiles, meta = await discovery_service.discover_creators(discovery_request, user_id)
            
            batch_results.append({
                "filter_set_index": i,
                "profiles": profiles,
                "meta": meta.dict()
            })
        
        # Calculate aggregated metadata
        total_profiles = sum(len(result["profiles"]) for result in batch_results)
        total_api_calls = sum(result["meta"]["external_api_calls"] for result in batch_results)
        
        aggregated_meta = {
            "total_filter_sets": len(request.filter_sets),
            "total_profiles_found": total_profiles,
            "total_external_api_calls": total_api_calls
        }
        
        return StandardResponse.success(
            data={
                "results": batch_results,
                "aggregated_meta": aggregated_meta
            },
            message=f"Batch discovery completed for {len(request.filter_sets)} filter sets"
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/quick-search", response_model=StandardResponseModel[dict])
async def quick_search(
    q: str = Query(..., min_length=1, description="Search query"),
    platform: Optional[str] = Query(None, description="Platform filter"),
    min_followers: Optional[int] = Query(None, description="Minimum follower count"),
    max_followers: Optional[int] = Query(None, description="Maximum follower count"),
    verified_only: Optional[bool] = Query(None, description="Verified accounts only"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=50, description="Results per page")
):
    """
    Quick search endpoint with simplified parameters
    
    Provides a simplified search interface for basic creator discovery
    without requiring complex filter objects.
    """
    try:
        # Build filters from query parameters
        filters = DiscoveryFilters()
        
        # Performance filters
        if min_followers is not None or max_followers is not None:
            from app.schemas.filter_schemas import PerformanceFilters, RangeFilter
            
            filters.performance = PerformanceFilters(
                follower_count=RangeFilter(
                    min_value=min_followers,
                    max_value=max_followers
                )
            )
        
        # Demographic filters
        if verified_only is not None:
            from app.schemas.filter_schemas import DemographicFilters
            
            filters.demographic = DemographicFilters(is_verified=verified_only)
        
        # Platform filters
        if platform:
            from app.schemas.filter_schemas import PlatformFilters
            
            filters.platform = PlatformFilters(platform=[platform])
        
        # Create discovery request
        discovery_request = DiscoveryRequest(
            filters=filters,
            search_query=q,
            view_mode=ViewMode.QUICK_VIEW,
            page=page,
            page_size=page_size
        )
        
        profiles, meta = await discovery_service.discover_creators(discovery_request)
        
        return StandardResponse.success(
            data={
                "profiles": profiles,
                "meta": meta.dict(),
                "search_query": q
            },
            message=f"Quick search completed: {meta.total_count} results"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Quick search failed: {str(e)}")


def _merge_filters(base_filters: DiscoveryFilters, additional_filters: DiscoveryFilters) -> DiscoveryFilters:
    """Merge two filter sets, with additional filters taking precedence"""
    # This is a simplified merge - in a full implementation,
    # you'd want more sophisticated merging logic
    
    merged_dict = base_filters.dict()
    additional_dict = additional_filters.dict(exclude_unset=True)
    
    # Simple merge strategy - additional filters override base filters
    for category, filters in additional_dict.items():
        if filters:
            if category in merged_dict:
                # Merge category filters
                if merged_dict[category]:
                    merged_dict[category].update(filters)
                else:
                    merged_dict[category] = filters
            else:
                merged_dict[category] = filters
    
    return DiscoveryFilters(**merged_dict)
