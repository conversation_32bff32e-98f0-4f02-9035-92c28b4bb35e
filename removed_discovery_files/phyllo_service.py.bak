"""
Phyllo API service for CreatorVerse Discovery Profile Analytics.

This module handles integration with Phyllo API for creator discovery,
profile analytics, and audience insights across social media platforms.
"""

import json
import httpx
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID
import random

from app.utilities.json_utils import json_dumps

from app.core.config import APP_CONFIG, get_locobuzz_redis
from app.core.exceptions import CreatorVerseError
from app.schemas.filter_schemas import (
    PhylloCreatorSearchRequest, PhylloCreatorProfile, 
    FilteredCreatorsResponse, ApplyFiltersRequest,
    PlatformEnum, OptionForTypeEnum
)


class PhylloAPIService:
    """Service for integrating with Phyllo API for creator discovery and analytics."""
    
    def __init__(self):
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self.redis = get_locobuzz_redis()
        self.base_url = APP_CONFIG.phyllo_api_url
        self.client_id = APP_CONFIG.phyllo_client_id
        self.client_secret = APP_CONFIG.phyllo_client_secret
        self._access_token = None
        self._token_expires_at = None
        
        # Testing mode settings
        self.use_mock_apis = APP_CONFIG.use_mock_apis
        self.mock_data_enabled = APP_CONFIG.mock_data_enabled
        self.disable_ssl_verification = APP_CONFIG.disable_ssl_verification
        
        # Log API configuration
        self.logger.info(f"Initializing Phyllo API service with URL: {self.base_url}")
        self.logger.info(f"SSL verification disabled: {self.disable_ssl_verification}")
        
        # Rate limiting
        self.requests_per_minute = APP_CONFIG.phyllo_requests_per_minute
        self.rate_limit_window = 60  # seconds
        
        # Filter mapping for different platforms
        self.filter_mappings = {
            PlatformEnum.instagram: {
                OptionForTypeEnum.creator: {
                    'gender': 'creator_gender',
                    'age': 'creator_age',
                    'location': 'creator_locations',
                    'language': 'creator_language',
                    'follower_count': 'follower_count',
                    'engagement_rate': 'engagement_rate',
                    'average_likes': 'average_likes',
                    'category': 'creator_interests',
                    'is_verified': 'is_verified',
                    'reel_views': 'reel_views',
                    'follower_growth': 'follower_growth'
                },
                OptionForTypeEnum.audience: {
                    'gender': 'audience_gender',
                    'age': 'audience_age',
                    'location': 'audience_locations',
                    'language': 'audience_language',
                    'interests': 'audience_interests',
                    'brand_affinity': 'audience_brand_affinities'
                }
            },
            PlatformEnum.youtube: {
                OptionForTypeEnum.creator: {
                    'gender': 'creator_gender',
                    'age': 'creator_age',
                    'location': 'creator_locations',
                    'language': 'creator_language',
                    'subscriber_count': 'subscriber_count',
                    'average_views': 'average_views',
                    'engagement_rate': 'engagement_rate',
                    'category': 'creator_interests',
                    'subscriber_growth': 'subscriber_growth'
                },
                OptionForTypeEnum.audience: {
                    'gender': 'audience_gender',
                    'age': 'audience_age',
                    'location': 'audience_locations',
                    'language': 'audience_language',
                    'interests': 'audience_interests',
                    'brand_affinity': 'audience_brand_affinities'
                }
            }
        }
    
    def _generate_mock_creator_data(self, count: int = 10) -> List[Dict[str, Any]]:
        """Generate mock creator data for testing."""
        mock_creators = []
        
        sample_names = [
            "Emily Johnson", "Michael Chen", "Sarah Williams", "David Rodriguez", 
            "Jessica Kim", "Alex Thompson", "Maria Garcia", "James Wilson",
            "Ashley Davis", "Ryan Miller", "Samantha Lee", "Justin Brown"
        ]
        
        sample_usernames = [
            "emily_lifestyle", "mike_tech", "sarah_fitness", "david_travel",
            "jessica_food", "alex_photography", "maria_fashion", "james_gaming",
            "ashley_beauty", "ryan_sports", "sam_art", "justin_music"
        ]
        
        categories = ["Lifestyle", "Technology", "Fitness", "Travel", "Food", 
                     "Photography", "Fashion", "Gaming", "Beauty", "Sports", "Art", "Music"]
        
        locations = ["Mumbai", "Delhi", "Bangalore", "Chennai", "Kolkata", "Pune", 
                    "Hyderabad", "Ahmedabad", "Jaipur", "Surat"]
        
        for i in range(min(count, len(sample_names))):
            creator = {
                "external_id": f"test_creator_{i+1}",
                "username": sample_usernames[i],
                "full_name": sample_names[i],
                "follower_count": random.randint(1000, 1000000),
                "engagement_rate": round(random.uniform(1.0, 15.0), 2),
                "creator_location": {"city": random.choice(locations)},
                "category": categories[i],
                "is_verified": random.choice([True, False]),
                "image_url": f"https://api.dicebear.com/7.x/personas/svg?seed={sample_usernames[i]}"
            }
            mock_creators.append(creator)
        
        return mock_creators
    
    def _generate_mock_analytics_data(self, creator_id: str) -> Dict[str, Any]:
        """Generate mock analytics data for a creator."""
        return {
            "creator_id": creator_id,
            "follower_growth": {
                "30_days": round(random.uniform(-5.0, 25.0), 2),
                "90_days": round(random.uniform(-10.0, 50.0), 2)
            },
            "engagement_metrics": {
                "average_likes": random.randint(100, 10000),
                "average_comments": random.randint(10, 500),
                "average_shares": random.randint(5, 200),
                "engagement_rate": round(random.uniform(1.0, 15.0), 2)
            },
            "content_performance": {
                "posts_count": random.randint(20, 200),
                "avg_reach": random.randint(1000, 100000),
                "top_performing_content": "lifestyle"
            },
            "audience_insights": {
                "primary_demographics": {
                    "age_group": "25-34",
                    "gender": random.choice(["male", "female", "mixed"]),
                    "location": "India"
                }
            }
        }
    
    def _generate_mock_audience_data(self, creator_id: str) -> Dict[str, Any]:
        """Generate mock audience insights data."""
        return {
            "creator_id": creator_id,
            "demographics": {
                "age_distribution": [
                    {"age_range": "13-17", "percentage": 5.2},
                    {"age_range": "18-24", "percentage": 25.8},
                    {"age_range": "25-34", "percentage": 35.4},
                    {"age_range": "35-44", "percentage": 22.1},
                    {"age_range": "45+", "percentage": 11.5}
                ],
                "gender_distribution": [
                    {"gender": "female", "percentage": 52.3},
                    {"gender": "male", "percentage": 46.2},
                    {"gender": "other", "percentage": 1.5}
                ],
                "location_distribution": [
                    {"country": "India", "percentage": 78.5},
                    {"country": "United States", "percentage": 8.2},
                    {"country": "United Kingdom", "percentage": 4.1},
                    {"country": "Canada", "percentage": 3.8},
                    {"country": "Australia", "percentage": 2.7},
                    {"country": "Other", "percentage": 2.7}
                ]
            },
            "interests": [
                {"interest": "Fashion", "percentage": 45.2},
                {"interest": "Travel", "percentage": 38.6},
                {"interest": "Food", "percentage": 32.1},
                {"interest": "Fitness", "percentage": 28.9},
                {"interest": "Technology", "percentage": 25.4}
            ],
            "brand_affinities": [
                {"brand": "Nike", "affinity_score": 8.2},
                {"brand": "Apple", "affinity_score": 7.8},
                {"brand": "Starbucks", "affinity_score": 7.1},
                {"brand": "Netflix", "affinity_score": 6.9},
                {"brand": "Amazon", "affinity_score": 6.5}
            ]
        }
    
    async def _search_creators_mock(
        self, 
        request: ApplyFiltersRequest, 
        start_time: datetime
    ) -> FilteredCreatorsResponse:
        """Mock implementation for creator search."""
        await asyncio.sleep(0.1)  # Simulate API delay
        
        # Generate mock creators
        mock_creators_data = self._generate_mock_creator_data(request.page_size)
        
        # Parse creators from mock data
        creators = []
        for creator_data in mock_creators_data:
            creator = PhylloCreatorProfile(
                external_id=creator_data.get('external_id', ''),
                username=creator_data.get('username', ''),
                full_name=creator_data.get('full_name'),
                follower_count=creator_data.get('follower_count'),
                engagement_rate=creator_data.get('engagement_rate'),
                location=creator_data.get('creator_location', {}).get('city'),
                category=creator_data.get('category'),
                is_verified=creator_data.get('is_verified'),
                avatar_url=creator_data.get('image_url')
            )
            creators.append(creator)
        
        # Mock pagination
        total_count = random.randint(50, 500)
        total_pages = (total_count + request.page_size - 1) // request.page_size
        
        # Calculate execution time
        execution_time_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
        
        self.logger.info(
            f"[MOCK] Found {len(creators)} creators in {execution_time_ms}ms "
            f"for {request.channel.value} {request.option_for.value}"
        )
        
        return FilteredCreatorsResponse(
            creators=creators,
            total_count=total_count,
            page=request.page,
            page_size=request.page_size,
            total_pages=total_pages,
            applied_filters=request.filters,
            execution_time_ms=execution_time_ms
        )
    
    async def _get_creator_analytics_mock(self, creator_id: str) -> Dict[str, Any]:
        """Mock implementation for creator analytics."""
        await asyncio.sleep(0.05)  # Simulate API delay
        
        analytics_data = self._generate_mock_analytics_data(creator_id)
        
        self.logger.info(f"[MOCK] Retrieved analytics for creator {creator_id}")
        return analytics_data
    
    async def _get_audience_insights_mock(self, creator_id: str) -> Dict[str, Any]:
        """Mock implementation for audience insights."""
        await asyncio.sleep(0.05)  # Simulate API delay
        
        audience_data = self._generate_mock_audience_data(creator_id)
        
        self.logger.info(f"[MOCK] Retrieved audience insights for creator {creator_id}")
        return audience_data
    
    async def _quick_search_mock(
        self, 
        query: str, 
        platform: PlatformEnum,
        limit: int
    ) -> List[PhylloCreatorProfile]:
        """Mock implementation for quick search."""
        await asyncio.sleep(0.05)  # Simulate API delay
        
        # Generate fewer results for quick search
        mock_creators_data = self._generate_mock_creator_data(min(limit, 5))
        
        # Filter by query (basic simulation)
        filtered_creators = []
        for creator_data in mock_creators_data:
            if (query.lower() in creator_data.get('username', '').lower() or 
                query.lower() in creator_data.get('full_name', '').lower()):
                filtered_creators.append(creator_data)
        
        # If no matches, return some results anyway for demo
        if not filtered_creators:
            filtered_creators = mock_creators_data[:3]
        
        # Parse creators
        creators = []
        for creator_data in filtered_creators:
            creator = PhylloCreatorProfile(
                external_id=creator_data.get('external_id', ''),
                username=creator_data.get('username', ''),
                full_name=creator_data.get('full_name'),
                follower_count=creator_data.get('follower_count'),
                engagement_rate=creator_data.get('engagement_rate'),
                location=creator_data.get('creator_location', {}).get('city'),
                category=creator_data.get('category'),
                is_verified=creator_data.get('is_verified'),
                avatar_url=creator_data.get('image_url')
            )
            creators.append(creator)
        
        self.logger.info(f"[MOCK] Quick search found {len(creators)} creators for query '{query}'")
        return creators
    
    async def _get_access_token(self) -> str:
        """Get or refresh access token for Phyllo API."""
        try:
            # Check if current token is still valid
            if (self._access_token and self._token_expires_at and 
                datetime.utcnow() < self._token_expires_at - timedelta(minutes=5)):
                return self._access_token
            
            # Check cache first
            cached_token = await self.redis.get("phyllo_access_token")
            if cached_token:
                token_data = json.loads(cached_token)
                self._access_token = token_data['access_token']
                self._token_expires_at = datetime.fromisoformat(token_data['expires_at'])
                return self._access_token
              # Request new token            async with httpx.AsyncClient(verify=not self.disable_ssl_verification) as client:
                self.logger.debug(f"Making request to {self.base_url}/auth/token with SSL verification: {not self.disable_ssl_verification}")
                response = await client.post(
                    f"{self.base_url}/auth/token",
                    json={
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "grant_type": "client_credentials"
                    },
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    raise CreatorVerseError(f"Failed to get Phyllo access token: {response.text}")
                
                token_data = response.json()
                self._access_token = token_data['access_token']
                expires_in = token_data.get('expires_in', 3600)  # default 1 hour
                self._token_expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
                
                # Cache the token
                cache_data = {
                    'access_token': self._access_token,
                    'expires_at': self._token_expires_at.isoformat()
                }
                await self.redis.setex(
                    "phyllo_access_token", 
                    expires_in - 300,  # Cache for 5 minutes less than actual expiry
                    json_dumps(cache_data)
                )
                
                self.logger.info("Successfully obtained new Phyllo access token")
                return self._access_token
                
        except Exception as e:
            self.logger.error(f"Error getting Phyllo access token: {str(e)}")
            raise CreatorVerseError(f"Failed to authenticate with Phyllo: {str(e)}")
      async def _make_api_request(
        self, 
        endpoint: str, 
        method: str = "POST",
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make authenticated request to Phyllo API with rate limiting."""
        try:
            # Rate limiting check
            await self._check_rate_limit()
            
            # Get access token
            access_token = await self._get_access_token()
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient(verify=not self.disable_ssl_verification) as client:
                self.logger.debug(f"Making request to {self.base_url}{endpoint} with SSL verification: {not self.disable_ssl_verification}")
                if method.upper() == "POST":
                    response = await client.post(
                        f"{self.base_url}{endpoint}",
                        json=data,
                        headers=headers,
                        params=params,
                        timeout=30.0
                    )
                else:
                    response = await client.get(
                        f"{self.base_url}{endpoint}",
                        headers=headers,
                        params=params,
                        timeout=30.0
                    )
                
                # Log rate limit usage
                await self._log_rate_limit_usage()
                
                if response.status_code == 429:
                    # Rate limit exceeded
                    retry_after = int(response.headers.get('Retry-After', 60))
                    raise CreatorVerseError(
                        f"Phyllo API rate limit exceeded. Retry after {retry_after} seconds"
                    )
                
                if response.status_code not in [200, 201]:
                    self.logger.error(f"Phyllo API error: {response.status_code} - {response.text}")
                    raise CreatorVerseError(f"Phyllo API request failed: {response.text}")
                
                return response.json()
                
        except httpx.TimeoutException:
            raise CreatorVerseError("Phyllo API request timed out")
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            self.logger.error(f"Error making Phyllo API request: {str(e)}")
            raise CreatorVerseError(f"Failed to communicate with Phyllo API: {str(e)}")
    
    async def _check_rate_limit(self) -> None:
        """Check if we're within rate limits."""
        try:
            current_minute = int(datetime.utcnow().timestamp()) // 60
            rate_limit_key = f"phyllo_rate_limit:{current_minute}"
            
            current_count = await self.redis.get(rate_limit_key)
            current_count = int(current_count) if current_count else 0
            
            if current_count >= self.requests_per_minute:
                raise CreatorVerseError(
                    f"Phyllo API rate limit exceeded ({self.requests_per_minute} requests/minute)"
                )
                
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            # Don't fail the request for rate limit checking errors
            self.logger.warning(f"Error checking rate limit: {str(e)}")
    
    async def _log_rate_limit_usage(self) -> None:
        """Log API usage for rate limiting."""
        try:
            current_minute = int(datetime.utcnow().timestamp()) // 60
            rate_limit_key = f"phyllo_rate_limit:{current_minute}"
            
            await self.redis.incr(rate_limit_key)
            await self.redis.expire(rate_limit_key, 120)  # Keep for 2 minutes
            
        except Exception as e:
            self.logger.warning(f"Error logging rate limit usage: {str(e)}")
    
    def _map_filters_to_phyllo(
        self, 
        filters: Dict[str, Any], 
        platform: PlatformEnum,
        option_for: OptionForTypeEnum
    ) -> Dict[str, Any]:
        """Map frontend filters to Phyllo API format."""
        try:
            mapping = self.filter_mappings.get(platform, {}).get(option_for, {})
            phyllo_filters = {}
            
            for filter_name, filter_value in filters.items():
                # Get the corresponding Phyllo field name
                phyllo_field = mapping.get(filter_name)
                if not phyllo_field:
                    self.logger.warning(f"No mapping found for filter: {filter_name}")
                    continue
                
                # Process different filter value types
                if isinstance(filter_value, dict) and 'min' in filter_value and 'max' in filter_value:
                    # Range filter
                    phyllo_filters[phyllo_field] = {
                        'min': filter_value['min'],
                        'max': filter_value['max']
                    }
                elif isinstance(filter_value, list):
                    # Multi-select filter
                    phyllo_filters[phyllo_field] = filter_value
                elif isinstance(filter_value, str) and filter_value.lower() in ['true', 'false']:
                    # Boolean filter
                    phyllo_filters[phyllo_field] = filter_value.lower() == 'true'
                else:
                    # Direct value
                    phyllo_filters[phyllo_field] = filter_value
            
            # Add platform-specific filters
            if platform == PlatformEnum.instagram:
                phyllo_filters['platform'] = 'instagram'
                # Add Instagram-specific options if available
                if option_for == OptionForTypeEnum.creator:
                    phyllo_filters['instagram_options'] = {}
            elif platform == PlatformEnum.youtube:
                phyllo_filters['platform'] = 'youtube'
            
            return phyllo_filters
            
        except Exception as e:
            self.logger.error(f"Error mapping filters: {str(e)}")
            raise CreatorVerseError(f"Failed to process filters: {str(e)}")
    
    async def search_creators(
        self, 
        request: ApplyFiltersRequest
    ) -> FilteredCreatorsResponse:
        """
        Search for creators using applied filters.
        
        Args:
            request: Filter application request
            
        Returns:
            Filtered creators response
        """
        start_time = datetime.utcnow()
        
        try:
            # Use mock data if in testing mode
            if self.use_mock_apis or self.mock_data_enabled:
                return await self._search_creators_mock(request, start_time)
            # Map filters to Phyllo format
            phyllo_filters = self._map_filters_to_phyllo(
                request.filters, 
                request.channel, 
                request.option_for
            )
            
            # Prepare search request
            search_data = {
                'filters': phyllo_filters,
                'limit': request.page_size,
                'offset': (request.page - 1) * request.page_size,
                'sort_by': request.sort_by or 'follower_count',
                'sort_order': request.sort_order
            }
            
            # Check cache first
            cache_key = f"creator_search:{hash(json_dumps(search_data, sort_keys=True))}"
            cached_result = await self.redis.get(cache_key)
            
            if cached_result:
                self.logger.info("Retrieved creator search results from cache")
                return FilteredCreatorsResponse.model_validate(json.loads(cached_result))
            
            # Make API request
            endpoint = "/v1/social/creator-profile/search"
            response_data = await self._make_api_request(endpoint, "POST", search_data)
            
            # Parse creators from response
            creators = []
            for creator_data in response_data.get('data', []):
                creator = PhylloCreatorProfile(
                    external_id=creator_data.get('external_id', ''),
                    username=creator_data.get('username', ''),
                    full_name=creator_data.get('full_name'),
                    follower_count=creator_data.get('follower_count'),
                    engagement_rate=creator_data.get('engagement_rate'),
                    location=creator_data.get('creator_location', {}).get('city'),
                    category=creator_data.get('category'),
                    is_verified=creator_data.get('is_verified'),
                    avatar_url=creator_data.get('image_url')
                )
                creators.append(creator)
            
            # Calculate pagination
            total_count = response_data.get('total_count', len(creators))
            total_pages = (total_count + request.page_size - 1) // request.page_size
            
            # Calculate execution time
            execution_time_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            # Create response
            filtered_response = FilteredCreatorsResponse(
                creators=creators,
                total_count=total_count,
                page=request.page,
                page_size=request.page_size,
                total_pages=total_pages,
                applied_filters=request.filters,
                execution_time_ms=execution_time_ms
            )
            
            # Cache the result
            await self.redis.setex(
                cache_key,
                APP_CONFIG.creator_data_ttl,
                filtered_response.model_dump_json()
            )
            
            self.logger.info(
                f"Found {len(creators)} creators in {execution_time_ms}ms "
                f"for {request.channel.value} {request.option_for.value}"
            )
            
            return filtered_response
            
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            self.logger.error(f"Error searching creators: {str(e)}")
            raise CreatorVerseError(f"Failed to search creators: {str(e)}")
    
    async def get_creator_analytics(
        self, 
        creator_id: str, 
        platform: PlatformEnum
    ) -> Dict[str, Any]:
        """
        Get detailed analytics for a specific creator.
        
        Args:
            creator_id: Creator's external ID
            platform: Platform type
            
        Returns:
            Creator analytics data
        """
        try:
            # Use mock data if in testing mode
            if self.use_mock_apis or self.mock_data_enabled:
                return await self._get_creator_analytics_mock(creator_id)
            # Check cache first
            cache_key = f"creator_analytics:{platform.value}:{creator_id}"
            cached_result = await self.redis.get(cache_key)
            
            if cached_result:
                return json.loads(cached_result)
            
            # Make API request
            endpoint = f"/v1/social/creator-profile/{creator_id}/analytics"
            params = {'platform': platform.value}
            
            analytics_data = await self._make_api_request(endpoint, "GET", params=params)
            
            # Cache the result
            await self.redis.setex(
                cache_key,
                APP_CONFIG.creator_data_ttl,
                json_dumps(analytics_data)
            )
            
            self.logger.info(f"Retrieved analytics for creator {creator_id}")
            return analytics_data
            
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            self.logger.error(f"Error getting creator analytics: {str(e)}")
            raise CreatorVerseError(f"Failed to get creator analytics: {str(e)}")
    
    async def get_audience_insights(
        self, 
        creator_id: str, 
        platform: PlatformEnum
    ) -> Dict[str, Any]:
        """
        Get audience insights for a specific creator.
        
        Args:
            creator_id: Creator's external ID
            platform: Platform type
            
        Returns:
            Audience insights data
        """
        try:
            # Use mock data if in testing mode
            if self.use_mock_apis or self.mock_data_enabled:
                return await self._get_audience_insights_mock(creator_id)
            # Check cache first
            cache_key = f"audience_insights:{platform.value}:{creator_id}"
            cached_result = await self.redis.get(cache_key)
            
            if cached_result:
                return json.loads(cached_result)
            
            # Make API request
            endpoint = f"/v1/social/creator-profile/{creator_id}/audience"
            params = {'platform': platform.value}
            
            audience_data = await self._make_api_request(endpoint, "GET", params=params)
            
            # Cache the result
            await self.redis.setex(
                cache_key,
                APP_CONFIG.audience_data_ttl,
                json_dumps(audience_data)
            )
            
            self.logger.info(f"Retrieved audience insights for creator {creator_id}")
            return audience_data
            
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            self.logger.error(f"Error getting audience insights: {str(e)}")
            raise CreatorVerseError(f"Failed to get audience insights: {str(e)}")
    
    async def quick_search(
        self, 
        query: str, 
        platform: PlatformEnum,
        limit: int = 10
    ) -> List[PhylloCreatorProfile]:
        """
        Perform quick search for creators by username or name.
        
        Args:
            query: Search query (username or name)
            platform: Platform type
            limit: Maximum number of results
            
        Returns:
            List of matching creators
        """
        try:
            # Use mock data if in testing mode
            if self.use_mock_apis or self.mock_data_enabled:
                return await self._quick_search_mock(query, platform, limit)
            # Check cache first
            cache_key = f"quick_search:{platform.value}:{query.lower()}:{limit}"
            cached_result = await self.redis.get(cache_key)
            
            if cached_result:
                return [PhylloCreatorProfile.model_validate(c) for c in json.loads(cached_result)]
            
            # Make API request
            endpoint = "/v1/social/creator-profile/quick-search"
            search_data = {
                'query': query,
                'platform': platform.value,
                'limit': limit
            }
            
            response_data = await self._make_api_request(endpoint, "POST", search_data)
            
            # Parse creators
            creators = []
            for creator_data in response_data.get('data', []):
                creator = PhylloCreatorProfile(
                    external_id=creator_data.get('external_id', ''),
                    username=creator_data.get('username', ''),
                    full_name=creator_data.get('full_name'),
                    follower_count=creator_data.get('follower_count'),
                    engagement_rate=creator_data.get('engagement_rate'),
                    location=creator_data.get('creator_location', {}).get('city'),
                    category=creator_data.get('category'),
                    is_verified=creator_data.get('is_verified'),
                    avatar_url=creator_data.get('image_url')
                )
                creators.append(creator)
            
            # Cache the result
            await self.redis.setex(
                cache_key,
                APP_CONFIG.default_ttl,
                json_dumps([c.model_dump() for c in creators])
            )
            
            self.logger.info(f"Quick search found {len(creators)} creators for query '{query}'")
            return creators
            
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            self.logger.error(f"Error in quick search: {str(e)}")
            raise CreatorVerseError(f"Quick search failed: {str(e)}")


# Global Phyllo service instance
phyllo_service = PhylloAPIService()
