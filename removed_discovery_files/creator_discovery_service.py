"""
Enhanced Creator Discovery Service - Two-mode discovery with Phyllo API integration
"""
import time
import json
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc, func, text
from sqlalchemy.orm import selectinload

from app.core.config import APP_CONFIG, get_database, get_discovery_redis
from app.core.exceptions import DiscoveryError, FilterValidationError, DatabaseError, CacheError
from app.models.enhanced_profile_models import EnhancedProfile as Profile
from app.schemas.discovery_schemas import (
    DiscoveryRequest, QuickViewProfile, DetailedProfile, DiscoveryMeta,
    SortField, SortOrder, ViewMode, DataSource
)
from app.schemas.filter_schemas import DiscoveryFilters, RangeFilter, PlatformEnum
from app.services.phyllo_api_client import PhylloAPIClient
from app.services.enhanced_filter_service import EnhancedFilterService
from app.core_helper.async_logger import with_trace_id


class EnhancedCreatorDiscoveryService:
    """Enhanced service for two-mode creator discovery with Phyllo API integration"""

    def __init__(self):
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self.redis_client = get_discovery_redis()
        self.db = get_database()
        self.phyllo_client = PhylloAPIClient()
        self.filter_service = EnhancedFilterService()
        
    @with_trace_id
    async def discover_creators(
        self,
        request: DiscoveryRequest,
        user_id: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], DiscoveryMeta]:
        """
        Enhanced two-mode discovery method with Phyllo API integration

        Mode 1: Quick View - Fast search from cached database + Phyllo Quick Search
        Mode 2: Analytics Mode - Detailed analytics via Phyllo Profile Analytics API
        """
        start_time = time.time()
        cache_hit = False
        external_api_calls = 0

        try:
            self.logger.info(f"Starting {request.view_mode.value} discovery with filters: {request.filters}")

            if request.view_mode == ViewMode.QUICK_VIEW:
                # Mode 1: Quick View Discovery
                profile_dicts, meta = await self._quick_view_discovery(request, start_time)
            else:
                # Mode 2: Detailed Analytics Discovery
                profile_dicts, meta = await self._analytics_mode_discovery(request, start_time)

            # Update metrics
            await self._update_discovery_metrics(user_id, meta.cache_hit, meta.external_api_calls)

            self.logger.info(f"Discovery completed: {len(profile_dicts)} profiles, {meta.query_time_ms}ms")
            return profile_dicts, meta

        except Exception as e:
            self.logger.error(f"Discovery failed: {str(e)}")
            raise DiscoveryError(f"Discovery failed: {str(e)}")

    @with_trace_id
    async def _quick_view_discovery(
        self,
        request: DiscoveryRequest,
        start_time: float
    ) -> Tuple[List[Dict[str, Any]], DiscoveryMeta]:
        """
        Quick View Mode: Fast search from database + Phyllo Quick Search API
        """
        cache_hit = False
        external_api_calls = 0

        # Generate cache key
        cache_key = self._generate_cache_key(request)

        # Try cache first
        if request.cache_preference != "fresh":
            cached_result = await self._get_from_cache(cache_key)
            if cached_result:
                cache_hit = True
                self.logger.info("Serving quick view results from cache")
                return self._process_cached_result(cached_result, request)

        # Query database using enhanced filter service
        profiles, total_count, filter_metadata = await self.filter_service.apply_advanced_filters(
            filters=request.filters,
            page=request.page,
            page_size=request.page_size,
            sort_by=[{"field": sort.field, "order": sort.order} for sort in (request.sort_by or [])]
        )

        # Convert to quick view format
        profile_dicts = [profile.to_quick_view_dict() for profile in profiles]

        # If we need more results and external data is allowed, use Phyllo Quick Search
        if (request.include_external and
            len(profiles) < request.page_size and
            request.page == 1 and
            request.search_query):

            try:
                platform = PlatformEnum(request.platform) if request.platform else PlatformEnum.instagram
                external_creators = await self.phyllo_client.quick_search(
                    query=request.search_query,
                    platform=platform,
                    limit=request.page_size - len(profiles)
                )

                # Convert external creators to our format
                for creator in external_creators:
                    profile_dicts.append(self._normalize_external_creator(creator))

                external_api_calls = 1
                total_count += len(external_creators)

            except Exception as e:
                self.logger.warning(f"External quick search failed: {e}")

        # Create metadata
        query_time_ms = int((time.time() - start_time) * 1000)
        meta = DiscoveryMeta(
            total_count=total_count,
            page=request.page,
            page_size=request.page_size,
            total_pages=(total_count + request.page_size - 1) // request.page_size,
            has_next=request.page * request.page_size < total_count,
            has_previous=request.page > 1,
            query_time_ms=query_time_ms,
            cache_hit=cache_hit,
            external_api_calls=external_api_calls,
            filters_applied=filter_metadata.get('filters_applied', {}),
            sort_criteria=[
                {"field": sort.field, "order": sort.order}
                for sort in (request.sort_by or [])
            ]
        )

        # Cache the result
        if request.cache_preference != "strict":
            await self._cache_result(cache_key, profile_dicts, meta)

        return profile_dicts, meta

    @with_trace_id
    async def _analytics_mode_discovery(
        self,
        request: DiscoveryRequest,
        start_time: float
    ) -> Tuple[List[Dict[str, Any]], DiscoveryMeta]:
        """
        Analytics Mode: Detailed discovery with Phyllo Advanced Search + Profile Analytics
        """
        cache_hit = False
        external_api_calls = 0

        # For analytics mode, we primarily use Phyllo Advanced Search API
        if request.include_external:
            try:
                platform = PlatformEnum(request.platform) if request.platform else PlatformEnum.instagram

                # Use Phyllo Advanced Search API
                external_creators, external_metadata = await self.phyllo_client.advanced_search(
                    filters=request.filters,
                    platform=platform,
                    page=request.page,
                    page_size=request.page_size
                )

                # For each creator, get detailed analytics if requested
                detailed_profiles = []
                for creator in external_creators:
                    if creator.get('external_id'):
                        try:
                            # Get detailed analytics using Profile Analytics API
                            analytics_data = await self.phyllo_client.get_profile_analytics(
                                creator_id=creator['external_id'],
                                platform=platform,
                                include_content=True,
                                include_audience=True
                            )

                            # Merge creator data with analytics
                            detailed_profile = self._merge_creator_with_analytics(creator, analytics_data)
                            detailed_profiles.append(detailed_profile)

                        except Exception as e:
                            self.logger.warning(f"Failed to get analytics for creator {creator.get('external_id')}: {e}")
                            # Fall back to basic creator data
                            detailed_profiles.append(creator)

                external_api_calls = 2  # Advanced search + profile analytics calls

                # Create metadata
                query_time_ms = int((time.time() - start_time) * 1000)
                meta = DiscoveryMeta(
                    total_count=external_metadata.get('total_count', len(detailed_profiles)),
                    page=request.page,
                    page_size=request.page_size,
                    total_pages=external_metadata.get('total_pages', 1),
                    has_next=external_metadata.get('has_next', False),
                    has_previous=request.page > 1,
                    query_time_ms=query_time_ms,
                    cache_hit=cache_hit,
                    external_api_calls=external_api_calls,
                    filters_applied=self._get_applied_filters_summary(request.filters),
                    sort_criteria=[
                        {"field": sort.field, "order": sort.order}
                        for sort in (request.sort_by or [])
                    ]
                )

                return detailed_profiles, meta

            except Exception as e:
                self.logger.warning(f"External analytics discovery failed: {e}")

        # Fallback to database query with enhanced filtering
        profiles, total_count, filter_metadata = await self.filter_service.apply_advanced_filters(
            filters=request.filters,
            page=request.page,
            page_size=request.page_size,
            sort_by=[{"field": sort.field, "order": sort.order} for sort in (request.sort_by or [])]
        )

        # Convert to detailed format
        profile_dicts = [profile.to_detailed_dict() for profile in profiles]

        # Create metadata
        query_time_ms = int((time.time() - start_time) * 1000)
        meta = DiscoveryMeta(
            total_count=total_count,
            page=request.page,
            page_size=request.page_size,
            total_pages=(total_count + request.page_size - 1) // request.page_size,
            has_next=request.page * request.page_size < total_count,
            has_previous=request.page > 1,
            query_time_ms=query_time_ms,
            cache_hit=cache_hit,
            external_api_calls=external_api_calls,
            filters_applied=filter_metadata.get('filters_applied', {}),
            sort_criteria=[
                {"field": sort.field, "order": sort.order}
                for sort in (request.sort_by or [])
            ]
        )

        return profile_dicts, meta

    def _normalize_external_creator(self, creator_data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize external creator data to our quick view format"""
        return {
            "id": creator_data.get('external_id', ''),
            "platform_username": creator_data.get('platform_username', ''),
            "full_name": creator_data.get('full_name', ''),
            "platform": creator_data.get('platform', 'instagram'),
            "image_url": creator_data.get('image_url', ''),
            "follower_count": creator_data.get('follower_count', 0),
            "engagement_rate": creator_data.get('engagement_rate', 0.0),
            "is_verified": creator_data.get('is_verified', False),
            "location": creator_data.get('location', ''),
            "primary_category": creator_data.get('category', ''),
            "is_external": True,
            "follower_tier": self._calculate_follower_tier(creator_data.get('follower_count', 0)),
            "engagement_tier": self._calculate_engagement_tier(creator_data.get('engagement_rate', 0.0))
        }

    def _merge_creator_with_analytics(self, creator_data: Dict[str, Any], analytics_data: Dict[str, Any]) -> Dict[str, Any]:
        """Merge basic creator data with detailed analytics from Phyllo"""
        # The analytics_data is already in the format we need (like profile_analytics.json)
        merged_data = analytics_data.copy()

        # Ensure we have the basic creator info
        if 'profile' in merged_data:
            profile = merged_data['profile']
            profile.update({
                'is_external': True,
                'data_source': 'phyllo'
            })

        return merged_data

    def _calculate_follower_tier(self, follower_count: int) -> str:
        """Calculate follower tier"""
        if follower_count >= 1_000_000:
            return "mega"
        elif follower_count >= 500_000:
            return "macro"
        elif follower_count >= 100_000:
            return "mid"
        elif follower_count >= 10_000:
            return "micro"
        elif follower_count >= 1_000:
            return "nano"
        else:
            return "small"

    def _calculate_engagement_tier(self, engagement_rate: float) -> str:
        """Calculate engagement tier"""
        if engagement_rate >= 10.0:
            return "very_high"
        elif engagement_rate >= 5.0:
            return "high"
        elif engagement_rate >= 2.0:
            return "average"
        else:
            return "low"

    @with_trace_id
    async def _query_profiles(
        self,
        session: AsyncSession,
        request: DiscoveryRequest
    ) -> Tuple[List[Profile], int]:
        """Query profiles from database based on filters"""
        try:
            # Build base query
            query = select(Profile)
            count_query = select(func.count(Profile.id))
            
            # Apply filters
            conditions = []
            if request.filters:
                self.logger.info(f"Building filter conditions for: {request.filters}")
                conditions = await self._build_filter_conditions(request.filters)
                self.logger.info(f"Built {len(conditions)} filter conditions")
            
            # Apply search query
            if request.search_query:
                search_conditions = self._build_search_conditions(
                    request.search_query, 
                    request.search_fields
                )
                conditions.extend(search_conditions)
            
            # Apply data source filter
            if request.data_source != DataSource.ALL:
                data_source_condition = self._build_data_source_condition(request.data_source)
                if data_source_condition is not None:
                    conditions.append(data_source_condition)
            
            # Combine all conditions
            if conditions:
                where_clause = and_(*conditions)
                query = query.where(where_clause)
                count_query = count_query.where(where_clause)
            
            # Get total count
            count_result = await session.execute(count_query)
            total_count = count_result.scalar()
            
            # Apply sorting
            if request.sort_by:
                for sort_criteria in request.sort_by:
                    sort_column = self._get_sort_column(sort_criteria.field)
                    if sort_criteria.order == SortOrder.DESC:
                        query = query.order_by(desc(sort_column))
                    else:
                        query = query.order_by(asc(sort_column))
            else:
                # Default sort by follower count descending
                query = query.order_by(desc(Profile.follower_count))
            
            # Apply pagination
            offset = (request.page - 1) * request.page_size
            query = query.offset(offset).limit(request.page_size)
            
            # Execute query
            result = await session.execute(query)
            profiles = result.scalars().all()
            
            return list(profiles), total_count
            
        except Exception as e:
            self.logger.error(f"Database query failed: {str(e)}")
            raise DatabaseError(f"Database query failed: {str(e)}")
    
    async def _build_filter_conditions(self, filters: DiscoveryFilters) -> List:
        """Build SQLAlchemy filter conditions from discovery filters"""
        conditions = []
        
        try:
            # Demographic filters
            if filters.demographic:
                demo = filters.demographic
                
                if demo.gender:
                    conditions.append(Profile.gender.in_(demo.gender))
                
                if demo.age_group:
                    conditions.append(Profile.age_group.in_(demo.age_group))
                
                # Skip location_tier filter as the column doesn't exist in the database
                # if demo.location_tier:
                #     conditions.append(Profile.location_tier.in_(demo.location_tier))
                
                if demo.country:
                    conditions.append(Profile.country.in_(demo.country))
                
                if demo.state:
                    conditions.append(Profile.state.in_(demo.state))
                
                if demo.city:
                    conditions.append(Profile.city.in_(demo.city))
                
                if demo.language:
                    conditions.append(Profile.language.in_(demo.language))
                
                if demo.is_verified is not None:
                    conditions.append(Profile.is_verified == demo.is_verified)
            
            # Performance filters
            if filters.performance:
                perf = filters.performance
                
                if perf.follower_count:
                    conditions.extend(self._build_range_conditions(
                        Profile.follower_count, perf.follower_count
                    ))
                
                if perf.engagement_rate:
                    conditions.extend(self._build_range_conditions(
                        Profile.engagement_rate, perf.engagement_rate
                    ))
                
                if perf.average_likes:
                    conditions.extend(self._build_range_conditions(
                        Profile.average_likes, perf.average_likes
                    ))
                
                if perf.average_comments:
                    conditions.extend(self._build_range_conditions(
                        Profile.average_comments, perf.average_comments
                    ))
                
                if perf.credibility_score:
                    conditions.extend(self._build_range_conditions(
                        Profile.credibility_score, perf.credibility_score
                    ))
                
                if perf.follower_growth_rate:
                    conditions.extend(self._build_range_conditions(
                        Profile.follower_growth_rate, perf.follower_growth_rate
                    ))
            
            # Content filters
            if filters.content:
                content = filters.content
                
                if content.primary_category:
                    conditions.append(Profile.primary_category.in_(content.primary_category))
                
                if content.interests:
                    # Use JSON contains for interests array
                    for interest in content.interests:
                        conditions.append(
                            func.json_extract_path_text(Profile.interests, '$').contains(interest)
                        )
            
            # Platform filters
            if filters.platform:
                platform = filters.platform
                
                # Platform filtering is now supported since we added the column
                if platform.platform:
                    conditions.append(Profile.platform.in_(platform.platform))
                
                if platform.account_type:
                    conditions.append(Profile.platform_account_type.in_(platform.account_type))
                
                if platform.data_source:
                    conditions.append(Profile.data_source.in_(platform.data_source))
                
                if platform.profile_status:
                    conditions.append(Profile.profile_status.in_(platform.profile_status))
                
                if platform.data_quality_score:
                    conditions.extend(self._build_range_conditions(
                        Profile.data_quality_score, platform.data_quality_score
                    ))
                
                if platform.last_updated:
                    # Filter by days since last update
                    cutoff_date = datetime.utcnow() - timedelta(days=platform.last_updated)
                    conditions.append(Profile.updated_at >= cutoff_date)
            
            return conditions
            
        except Exception as e:
            self.logger.error(f"Filter condition building failed: {str(e)}")
            raise FilterValidationError(f"Invalid filter criteria: {str(e)}")
    
    def _build_range_conditions(self, column, range_filter: RangeFilter) -> List:
        """Build range conditions for a column"""
        conditions = []
        
        if range_filter.min_value is not None:
            conditions.append(column >= range_filter.min_value)
        
        if range_filter.max_value is not None:
            conditions.append(column <= range_filter.max_value)
        
        return conditions
    
    def _build_search_conditions(self, search_query: str, search_fields: Optional[List[str]]) -> List:
        """Build search conditions for text search"""
        conditions = []
        
        # Default search fields if none specified
        if not search_fields:
            search_fields = ["platform_username", "full_name", "introduction"]
        
        search_conditions = []
        for field in search_fields:
            if hasattr(Profile, field):
                column = getattr(Profile, field)
                search_conditions.append(column.ilike(f"%{search_query}%"))
        
        if search_conditions:
            conditions.append(or_(*search_conditions))
        
        return conditions
    
    def _build_data_source_condition(self, data_source: DataSource):
        """Build condition for data source filtering"""
        if data_source == DataSource.INTERNAL_ONLY:
            return Profile.user_id.is_not(None)
        elif data_source == DataSource.EXTERNAL_ONLY:
            return Profile.user_id.is_(None)
        elif data_source == DataSource.PREFER_INTERNAL:
            # This would be handled in sorting, not filtering
            return None
        elif data_source == DataSource.PREFER_EXTERNAL:
            # This would be handled in sorting, not filtering
            return None
        
        return None
    
    def _get_sort_column(self, field: SortField):
        """Get SQLAlchemy column for sorting"""
        field_mapping = {
            SortField.FOLLOWER_COUNT: Profile.follower_count,
            SortField.ENGAGEMENT_RATE: Profile.engagement_rate,
            SortField.CREDIBILITY_SCORE: Profile.credibility_score,
            SortField.AVERAGE_LIKES: Profile.average_likes,
            SortField.AVERAGE_VIEWS: Profile.average_views,
            SortField.CREATED_AT: Profile.created_at,
            SortField.UPDATED_AT: Profile.updated_at,
            SortField.PLATFORM_USERNAME: Profile.platform_username,
            SortField.FULL_NAME: Profile.full_name,
        }
        
        return field_mapping.get(field, Profile.follower_count)
    
    async def _to_detailed_dict(self, profile: Profile) -> Dict[str, Any]:
        """Convert Profile model to detailed dictionary"""
        # Parse JSON fields
        secondary_categories = []
        interests = []
        brand_affinity = []
        audience_age_groups = {}
        audience_gender = {}
        audience_locations = {}
        audience_interests = []
        
        try:
            if profile.secondary_categories:
                secondary_categories = json.loads(profile.secondary_categories)
            if profile.interests:
                interests = json.loads(profile.interests)
            if profile.brand_affinity:
                brand_affinity = json.loads(profile.brand_affinity)
            if profile.audience_age_groups:
                audience_age_groups = json.loads(profile.audience_age_groups)
            if profile.audience_gender:
                audience_gender = json.loads(profile.audience_gender)
            if profile.audience_locations:
                audience_locations = json.loads(profile.audience_locations)
            if profile.audience_interests:
                audience_interests = json.loads(profile.audience_interests)
        except json.JSONDecodeError:
            pass  # Keep empty defaults
        
        return {
            "id": str(profile.id),
            "work_platform_id": str(profile.work_platform_id),
            "user_id": str(profile.user_id) if profile.user_id else None,
            "external_id": profile.external_id,
            "platform_username": profile.platform_username,
            "platform": profile.platform,
            "url": profile.url,
            "image_url": profile.image_url,
            "introduction": profile.introduction,
            "platform_account_type": profile.platform_account_type,
            "full_name": profile.full_name,
            "gender": profile.gender,
            "age_group": profile.age_group,
            "language": profile.language,
            "country": profile.country,
            "state": profile.state,
            "city": profile.city,
            "location_tier": profile.location_tier,
            "is_verified": profile.is_verified,
            "profile_status": profile.profile_status,
            "content_count": profile.content_count,
            "follower_count": profile.follower_count,
            "following_count": profile.following_count,
            "subscriber_count": profile.subscriber_count,
            "average_likes": profile.average_likes,
            "average_comments": profile.average_comments,
            "average_views": profile.average_views,
            "average_reels_views": profile.average_reels_views,
            "average_shares": profile.average_shares,
            "engagement_rate": profile.engagement_rate,
            "credibility_score": profile.credibility_score,
            "data_quality_score": profile.data_quality_score,
            "follower_growth_rate": profile.follower_growth_rate,
            "follower_growth_30d": profile.follower_growth_30d,
            "follower_growth_90d": profile.follower_growth_90d,
            "primary_category": profile.primary_category,
            "secondary_categories": secondary_categories,
            "interests": interests,
            "brand_affinity": brand_affinity,
            "audience_age_groups": audience_age_groups,
            "audience_gender": audience_gender,
            "audience_locations": audience_locations,
            "audience_interests": audience_interests,
            "data_source": profile.data_source,
            "is_external": profile.is_external_creator,
            "last_updated_external": profile.last_updated_external.isoformat() if profile.last_updated_external else None,
            "cache_expires_at": profile.cache_expires_at.isoformat() if profile.cache_expires_at else None,
            "created_at": profile.created_at.isoformat(),
            "updated_at": profile.updated_at.isoformat(),
            "follower_tier": profile.follower_tier,
            "engagement_tier": profile.engagement_tier
        }
    
    async def _fetch_external_profiles(self, request: DiscoveryRequest) -> Tuple[List[Dict[str, Any]], int]:
        """Fetch additional profiles from external APIs"""
        # Placeholder for external API integration
        # This would integrate with Phyllo or other APIs
        self.logger.info("External profile fetching not implemented yet")
        return [], 0
    
    def _generate_cache_key(self, request: DiscoveryRequest) -> str:
        """Generate cache key for discovery request"""
        import hashlib
        
        # Create a hash of the request parameters
        request_dict = request.dict()
        request_str = json.dumps(request_dict, sort_keys=True)
        hash_value = hashlib.md5(request_str.encode()).hexdigest()
        
        return f"discovery:query:{hash_value}"
    
    async def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached discovery results"""
        try:
            cached_data = await self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            self.logger.warning(f"Cache retrieval failed: {e}")
        
        return None
    
    async def _cache_result(self, cache_key: str, profiles: List[Dict[str, Any]], meta: DiscoveryMeta):
        """Cache discovery results"""
        try:
            cache_data = {
                "profiles": profiles,
                "meta": meta.dict(),
                "cached_at": datetime.utcnow().isoformat()
            }
            
            await self.redis_client.set(
                cache_key,
                json.dumps(cache_data),
                expire=APP_CONFIG.profile_cache_ttl
            )
        except Exception as e:
            self.logger.warning(f"Cache storage failed: {e}")
    
    def _process_cached_result(self, cached_result: Dict[str, Any], request: DiscoveryRequest) -> Tuple[List[Dict[str, Any]], DiscoveryMeta]:
        """Process cached discovery results"""
        profiles = cached_result["profiles"]
        meta_dict = cached_result["meta"]
        meta_dict["cache_hit"] = True
        meta = DiscoveryMeta(**meta_dict)
        
        return profiles, meta
    
    def _get_applied_filters_summary(self, filters: Optional[DiscoveryFilters]) -> Dict[str, Any]:
        """Get summary of applied filters for metadata"""
        if not filters:
            return {}
        
        summary = {}
        
        if filters.demographic:
            demo_count = sum(1 for field in [
                filters.demographic.gender,
                filters.demographic.age_group,
                filters.demographic.location_tier,
                filters.demographic.country,
                filters.demographic.language,
                filters.demographic.is_verified
            ] if field is not None)
            summary["demographic_filters"] = demo_count
        
        if filters.performance:
            perf_count = sum(1 for field in [
                filters.performance.follower_count,
                filters.performance.engagement_rate,
                filters.performance.average_likes,
                filters.performance.credibility_score
            ] if field is not None)
            summary["performance_filters"] = perf_count
        
        if filters.content:
            content_count = sum(1 for field in [
                filters.content.primary_category,
                filters.content.interests,
                filters.content.brand_affinity
            ] if field is not None)
            summary["content_filters"] = content_count
        
        return summary
    
    async def _update_discovery_metrics(self, user_id: Optional[str], cache_hit: bool, external_api_calls: int):
        """Update discovery metrics in Redis"""
        try:
            # Increment counters
            await self.redis_client.hincrby("discovery:metrics", "total_requests", 1)
            
            if cache_hit:
                await self.redis_client.hincrby("discovery:metrics", "cache_hits", 1)
            
            if external_api_calls > 0:
                await self.redis_client.hincrby("discovery:metrics", "external_api_calls", external_api_calls)
            
            # Track user-specific metrics if user_id provided
            if user_id:
                user_key = f"discovery:user_metrics:{user_id}"
                await self.redis_client.hincrby(user_key, "requests", 1)
                await self.redis_client.expire(user_key, 86400)  # 24 hour expiry
                
        except Exception as e:
            self.logger.warning(f"Metrics update failed: {e}")


# Create an alias for backward compatibility
CreatorDiscoveryService = EnhancedCreatorDiscoveryService
