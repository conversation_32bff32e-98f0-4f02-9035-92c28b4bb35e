"""
Phyllo API Provider Implementation
Converts frontend filter format to Phyllo API specific format.
"""
import asyncio
import json
import httpx
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import random
from pydantic import BaseModel

from app.core.config import APP_CONFIG, get_locobuzz_redis
from app.core.exceptions import CreatorVerseError, FilterValidationError
from app.core_helper.async_logger import with_trace_id
from app.schemas.external_api.provider_interface import (
    ExternalAPIProvider, APIProviderConfig, APIProviderType, APIProviderCapability,
    FilterMapping, extract_range_values, extract_list_values, normalize_boolean_value
)
from app.schemas.external_api.frontend_schemas import (
    FrontendFilterSelections, CreatorProfile, PlatformEnum, OptionForEnum
)


class PhylloConfig(APIProviderConfig):
    """Phyllo API configuration"""
    
    def __init__(self):
        self.client_id = APP_CONFIG.phyllo_client_id
        self.client_secret = APP_CONFIG.phyllo_client_secret
        # Use configured base URL for API
        self.api_url = APP_CONFIG.phyllo_base_url
        # Default to False if not explicitly set in config
        self.use_mock = getattr(APP_CONFIG, 'use_mock_apis', False)
        self.disable_ssl = getattr(APP_CONFIG, 'disable_ssl_verification', False)
    
    @property
    def provider_type(self) -> APIProviderType:
        return APIProviderType.PHYLLO
    
    @property
    def base_url(self) -> str:
        return self.api_url
    
    @property
    def capabilities(self) -> List[APIProviderCapability]:
        return [
            APIProviderCapability.CREATOR_SEARCH,
            APIProviderCapability.QUICK_SEARCH,
            APIProviderCapability.CREATOR_ANALYTICS,
            APIProviderCapability.AUDIENCE_INSIGHTS
        ]
    
    def get_filter_mapping(self, platform: PlatformEnum, option_for: OptionForEnum) -> FilterMapping:
        """Get Phyllo specific filter mappings"""
        mappings = {
            PlatformEnum.INSTAGRAM: {
                OptionForEnum.CREATOR: {
                    "gender": "creator_gender",
                    "age": "creator_age",
                    "location": "creator_locations",
                    "language": "creator_language",
                    "follower_count": "follower_count",
                    "engagement_rate": "engagement_rate",
                    "average_likes": "average_likes",
                    "average_comments": "average_comments",
                    "category": "creator_interests",
                    "interests": "creator_interests",
                    "is_verified": "is_verified",
                    "verification": "is_verified",
                    "reel_views": "reel_views",
                    "follower_growth": "follower_growth",
                    "credibility_score": "credibility_score"
                },
                OptionForEnum.AUDIENCE: {
                    "gender": "audience_gender",
                    "age": "audience_age",
                    "location": "audience_locations",
                    "language": "audience_language",
                    "interests": "audience_interests",
                    "brand_affinity": "audience_brand_affinities",
                    "brand_affinities": "audience_brand_affinities"
                }
            },
            PlatformEnum.YOUTUBE: {
                OptionForEnum.CREATOR: {
                    "gender": "creator_gender",
                    "age": "creator_age",
                    "location": "creator_locations",
                    "language": "creator_language",
                    "subscriber_count": "subscriber_count",
                    "average_views": "average_views",
                    "engagement_rate": "engagement_rate",
                    "category": "creator_interests",
                    "interests": "creator_interests",
                    "is_verified": "is_verified",
                    "verification": "is_verified",
                    "subscriber_growth": "subscriber_growth",
                    "credibility_score": "credibility_score"
                },
                OptionForEnum.AUDIENCE: {
                    "gender": "audience_gender",
                    "age": "audience_age",
                    "location": "audience_locations",
                    "language": "audience_language",
                    "interests": "audience_interests",
                    "brand_affinity": "audience_brand_affinities"
                }
            },
            PlatformEnum.TIKTOK: {
                OptionForEnum.CREATOR: {
                    "gender": "creator_gender",
                    "age": "creator_age",
                    "location": "creator_locations",
                    "language": "creator_language",
                    "follower_count": "follower_count",
                    "engagement_rate": "engagement_rate",
                    "average_likes": "average_likes",
                    "category": "creator_interests",
                    "is_verified": "is_verified"
                },
                OptionForEnum.AUDIENCE: {
                    "gender": "audience_gender",
                    "age": "audience_age",
                    "location": "audience_locations",
                    "interests": "audience_interests"
                }
            }
        }
        
        mapping = mappings.get(platform, {}).get(option_for, {})
        return FilterMapping(mapping)


def make_json_serializable(obj: Any) -> Any:
    """Convert objects to JSON-serializable format"""
    if isinstance(obj, BaseModel):
        return obj.dict()
    elif hasattr(obj, '__dict__'):
        # Handle custom objects with attributes
        return {k: make_json_serializable(v) for k, v in obj.__dict__.items()}
    elif isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [make_json_serializable(item) for item in obj]
    elif isinstance(obj, (str, int, float, bool, type(None))):
        return obj
    else:
        # For any other object, try to convert to string
        return str(obj)


class PhylloProvider(ExternalAPIProvider):
    """Phyllo API provider implementation"""
    
    def __init__(self, config: PhylloConfig):
        super().__init__(config)
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self.redis = get_locobuzz_redis()
        self._access_token = None
        self._token_expires_at = None
        # Localize config flags
        self.use_mock = config.use_mock
        self.disable_ssl = config.disable_ssl
        # Rate limiting
        self.requests_per_minute = getattr(APP_CONFIG, 'phyllo_requests_per_minute', 60)
    
    @with_trace_id
    async def search_creators(
        self,
        filter_selections: FrontendFilterSelections,
        **kwargs
    ) -> Tuple[List[CreatorProfile], Dict[str, Any]]:
        """Search creators using phyllo_dummy service"""
        start_time = datetime.utcnow()
        
        try:
            # Try to call the actual phyllo_dummy endpoints
            # Common endpoints for creator search APIs:
            # /api/v1/creators/search
            # /api/v1/profiles/search  
            # /search
            # /creators
            
            # Transform filters to JSON-serializable format
            transformed_filters = self.transform_filters(filter_selections)
            
            search_data = {
                "platform": filter_selections.channel.value if filter_selections.channel else "instagram",
                "filters": transformed_filters,
                "limit": filter_selections.pageSize or 20,
                "offset": ((filter_selections.page or 1) - 1) * (filter_selections.pageSize or 20)
            }
            
            # Ensure all data is JSON serializable
            search_data = make_json_serializable(search_data)
            
            # Call the phyllo_dummy service - try most common endpoints first
            endpoints_to_try = [
                "/",  # Root endpoint might handle POST
                "/api",  # Common API root
                "/creators",  # Simple creators endpoint
                "/profiles",  # Alternative naming
                "/api/creators",  # API with creators
                "/api/profiles",  # API with profiles
            ]
            
            response_data = None
            successful_endpoint = None
            
            for endpoint in endpoints_to_try:
                try:
                    response_data = await self._make_api_request(endpoint, "POST", search_data)
                    successful_endpoint = endpoint
                    self.logger.info(f"Successfully called phyllo_dummy endpoint: {endpoint}")
                    break
                except Exception as e:
                    self.logger.debug(f"Failed to call {endpoint}: {str(e)}")
                    continue
            
            if response_data is None:
                # If all endpoints fail, give clear error with endpoint list
                tried_endpoints = ', '.join(endpoints_to_try)
                self.logger.error(f"All phyllo_dummy endpoints failed. Tried: {tried_endpoints}")
                raise CreatorVerseError(
                    f"Phyllo_dummy service endpoints not found. "
                    f"Tried: {tried_endpoints}. "
                    f"Check if phyllo_dummy is running on {self.config.base_url} and verify endpoints with /docs"
                )
            
            # Parse response from phyllo_dummy
            creators = self._parse_phyllo_dummy_response(response_data.get("data", []))
            
            # Calculate metadata
            total_count = response_data.get("total_count", len(creators))
            execution_time_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            metadata = {
                "total_count": total_count,
                "execution_time_ms": execution_time_ms,
                "api_provider": "phyllo_dummy", 
                "cache_hit": False,
                "endpoint_used": successful_endpoint
            }
            
            self.logger.info(f"Phyllo_dummy search completed: {len(creators)} creators found in {execution_time_ms}ms")
            return creators, metadata
            
        except Exception as e:
            self.logger.error(f"Phyllo search failed: {str(e)}")
            raise CreatorVerseError(f"Phyllo API search failed: {str(e)}")
    
    @with_trace_id
    async def quick_search(
        self,
        query: str,
        platform: PlatformEnum,
        limit: int = 10,
        **kwargs
    ) -> List[CreatorProfile]:
        """Quick search using phyllo_dummy service"""
        try:
            # Validate inputs
            if not query or not query.strip():
                raise FilterValidationError("Search query cannot be empty")
            
            # Try to call phyllo_dummy quick search endpoint
            search_data = {
                "query": query,
                "platform": platform.value,
                "limit": limit
            }
            
            # Ensure all data is JSON serializable
            search_data = make_json_serializable(search_data)
            
            try:
                response_data = await self._make_api_request("/api/quick-search", "POST", search_data)
                if response_data and "data" in response_data:
                    return self._parse_phyllo_dummy_response(response_data["data"])
            except Exception as e:
                self.logger.warning(f"Phyllo_dummy quick search failed: {str(e)}, falling back to mock data")
            
            # Fallback to mock data only if phyllo_dummy is unavailable
            return await self._mock_quick_search(query, platform, limit)
            
        except Exception as e:
            if isinstance(e, (CreatorVerseError, FilterValidationError)):
                raise
            self.logger.error(f"Quick search failed: {str(e)}")
            raise CreatorVerseError(f"Quick search failed: {str(e)}")
    
    def transform_filters(self, filter_selections: FrontendFilterSelections) -> Dict[str, Any]:
        """Transform frontend filters to Phyllo API format"""
        try:
            # Get filter mapping for platform and target
            mapping = self.config.get_filter_mapping(
                filter_selections.channel,
                filter_selections.optionFor
            )
            
            phyllo_filters = {}
            
            # Add platform information
            phyllo_filters["platform"] = filter_selections.channel.value
            
            # Transform each filter
            for frontend_field, value in filter_selections.filters.items():
                # Skip empty values
                if value is None or value == "" or value == []:
                    continue
                
                # Get Phyllo field name
                phyllo_field = mapping.get(frontend_field)
                if not phyllo_field:
                    self.logger.warning(f"No Phyllo mapping for filter: {frontend_field}")
                    continue
                
                # Transform value based on type
                transformed_value = self._transform_filter_value(frontend_field, value)
                if transformed_value is not None:
                    phyllo_filters[phyllo_field] = transformed_value
            
            # Add platform-specific options
            if filter_selections.channel == PlatformEnum.INSTAGRAM:
                if filter_selections.optionFor == OptionForEnum.CREATOR:
                    phyllo_filters["instagram_options"] = {}
            
            self.logger.debug(f"Transformed filters: {phyllo_filters}")
            return phyllo_filters
            
        except Exception as e:
            self.logger.error(f"Filter transformation failed: {str(e)}")
            raise CreatorVerseError(f"Failed to transform filters: {str(e)}")
    
    def _transform_filter_value(self, field_name: str, value: Any) -> Any:
        """Transform a single filter value"""
        # Handle range values (follower_count, engagement_rate, etc.)
        if field_name in ["follower_count", "engagement_rate", "average_likes", "average_comments", 
                         "average_views", "subscriber_count", "credibility_score"]:
            range_val = extract_range_values(value)
            if range_val:
                return range_val
        
        # Handle list values (gender, age, location, etc.)
        if field_name in ["gender", "age", "location", "category", "interests", "language"]:
            list_val = extract_list_values(value)
            if list_val:
                return list_val
        
        # Handle boolean values (is_verified, verification)
        if field_name in ["is_verified", "verification"]:
            bool_val = normalize_boolean_value(value)
            if bool_val is not None:
                return bool_val
        
        # Handle special location mapping
        if field_name == "location":
            # Flatten multilevel location selections
            if isinstance(value, list):
                return value
            elif isinstance(value, dict):
                # Extract cities from tier structure
                cities = []
                for tier_data in value.values():
                    if isinstance(tier_data, list):
                        cities.extend(tier_data)
                return cities if cities else None
        
        # Handle age group mapping
        if field_name == "age":
            age_mappings = {
                "teen": "13-19",
                "young_adult": "20-35", 
                "adult": "36-55",
                "senior": "56+"
            }
            if isinstance(value, list):
                return [age_mappings.get(v, v) for v in value]
            elif isinstance(value, str):
                return [age_mappings.get(value, value)]
        
        # Default: return as-is
        return value
    
    async def get_creator_analytics(
        self,
        creator_id: str,
        platform: PlatformEnum,
        **kwargs
    ) -> Dict[str, Any]:
        """Get creator analytics from Phyllo"""
        try:
            if self.config.use_mock:
                return await self._mock_creator_analytics(creator_id)
            
            endpoint = f"/v1/social/creator-profile/{creator_id}/analytics"
            params = {"platform": platform.value}
            
            return await self._make_api_request(endpoint, "GET", params=params)
            
        except Exception as e:
            self.logger.error(f"Phyllo analytics failed: {str(e)}")
            raise CreatorVerseError(f"Failed to get creator analytics: {str(e)}")
    
    async def get_audience_insights(
        self,
        creator_id: str,
        platform: PlatformEnum,
        **kwargs
    ) -> Dict[str, Any]:
        """Get audience insights from Phyllo"""
        try:
            if self.config.use_mock:
                return await self._mock_audience_insights(creator_id)
            
            endpoint = f"/v1/social/creator-profile/{creator_id}/audience"
            params = {"platform": platform.value}
            
            return await self._make_api_request(endpoint, "GET", params=params)
            
        except Exception as e:
            self.logger.error(f"Phyllo audience insights failed: {str(e)}")
            raise CreatorVerseError(f"Failed to get audience insights: {str(e)}")
    
    def _parse_phyllo_dummy_response(self, data: List[Dict[str, Any]]) -> List[CreatorProfile]:
        """Parse phyllo_dummy API response to CreatorProfile objects"""
        creators = []
        
        for item in data:
            try:
                creator = CreatorProfile(
                    id=item.get("id", ""),
                    external_id=item.get("external_id", ""),
                    platform_username=item.get("platform_username", ""),
                    full_name=item.get("full_name"),
                    platform=item.get("platform", "instagram"),
                    url=item.get("url"),
                    image_url=item.get("image_url"),
                    follower_count=item.get("follower_count"),
                    engagement_rate=item.get("engagement_rate"),
                    is_verified=item.get("is_verified"),
                    location=item.get("location"),
                    category=item.get("category"),
                    data_source="phyllo_dummy"
                )
                creators.append(creator)
            except Exception as e:
                self.logger.warning(f"Failed to parse creator data: {e}")
                continue
        
        return creators
    
    # ============ MOCK DATA METHODS ============
    
    async def _mock_search_creators(self, filter_selections: FrontendFilterSelections) -> Tuple[List[CreatorProfile], Dict[str, Any]]:
        """Mock creator search for testing"""
        await asyncio.sleep(0.1)  # Simulate API delay
        
        # Generate mock creators based on filters
        mock_creators = self._generate_mock_creators(filter_selections.pageSize or 20)
        
        metadata = {
            "total_count": random.randint(50, 500),
            "execution_time_ms": random.randint(100, 500),
            "api_provider": "phyllo_mock",
            "cache_hit": False
        }
        
        self.logger.info(f"[MOCK] Generated {len(mock_creators)} creators for {filter_selections.channel.value}")
        return mock_creators, metadata
    
    async def _mock_quick_search(self, query: str, platform: PlatformEnum, limit: int) -> List[CreatorProfile]:
        """Mock quick search for testing"""
        await asyncio.sleep(0.05)
        
        mock_creators = self._generate_mock_creators(min(limit, 5))
        
        # Filter based on query
        filtered = []
        for creator in mock_creators:
            if (query.lower() in creator.platform_username.lower() or 
                (creator.full_name and query.lower() in creator.full_name.lower())):
                filtered.append(creator)
        
        return filtered if filtered else mock_creators[:3]
    
    async def _mock_creator_analytics(self, creator_id: str) -> Dict[str, Any]:
        """Mock creator analytics"""
        return {
            "creator_id": creator_id,
            "follower_growth": {
                "30_days": round(random.uniform(-5.0, 25.0), 2),
                "90_days": round(random.uniform(-10.0, 50.0), 2)
            },
            "engagement_metrics": {
                "average_likes": random.randint(100, 10000),
                "average_comments": random.randint(10, 500),
                "engagement_rate": round(random.uniform(1.0, 15.0), 2)
            }
        }
    
    async def _mock_audience_insights(self, creator_id: str) -> Dict[str, Any]:
        """Mock audience insights"""
        return {
            "creator_id": creator_id,
            "demographics": {
                "age_distribution": [
                    {"age_range": "18-24", "percentage": 25.8},
                    {"age_range": "25-34", "percentage": 35.4},
                    {"age_range": "35-44", "percentage": 22.1}
                ],
                "gender_distribution": [
                    {"gender": "female", "percentage": 52.3},
                    {"gender": "male", "percentage": 46.2}
                ]
            }
        }
    
    def _generate_mock_creators(self, count: int) -> List[CreatorProfile]:
        """Generate mock creator profiles with more diversity"""
        names = [
            "Emily Johnson", "Michael Chen", "Sarah Williams", "David Rodriguez", 
            "Jessica Kim", "Alex Thompson", "Maria Garcia", "James Wilson",
            "Priya Sharma", "Ahmed Hassan", "Lisa Zhang", "Carlos Silva",
            "Ayesha Patel", "Ryan O'Connor", "Nina Volkov", "Hassan Ali",
            "Sophie Martin", "Raj Malhotra", "Emma Davis", "Kevin Park"
        ]
        usernames = [
            "emily_lifestyle", "mike_tech", "sarah_fitness", "david_travel",
            "jessica_food", "alex_photography", "maria_fashion", "james_gaming",
            "priya_dance", "ahmed_comedy", "lisa_beauty", "carlos_music",
            "ayesha_art", "ryan_sports", "nina_books", "hassan_cooking",
            "sophie_pets", "raj_business", "emma_diy", "kevin_finance"
        ]
        categories = [
            "Lifestyle", "Technology", "Fitness", "Travel", "Food", "Photography", 
            "Fashion", "Gaming", "Dance", "Comedy", "Beauty", "Music",
            "Art", "Sports", "Books", "Cooking", "Pets", "Business", "DIY", "Finance"
        ]
        locations = [
            "Mumbai", "Delhi", "Bangalore", "Chennai", "Kolkata", "Pune", 
            "Hyderabad", "Ahmedabad", "Jaipur", "Lucknow", "Kanpur", "Nagpur",
            "Indore", "Thane", "Bhopal", "Visakhapatnam", "Pimpri", "Patna",
            "Vadodara", "Ghaziabad"
        ]
        
        creators = []
        for i in range(min(count, len(names))):
            creator = CreatorProfile(
                id=f"mock_creator_{i+1}",
                external_id=f"phyllo_{1000 + i}",
                platform_username=usernames[i],
                full_name=names[i],
                platform="instagram",
                url=f"https://instagram.com/{usernames[i]}",
                image_url=f"https://api.dicebear.com/7.x/personas/svg?seed={usernames[i]}",
                follower_count=random.randint(1000, 2000000),
                engagement_rate=round(random.uniform(0.5, 12.0), 2),
                is_verified=random.choice([True, False, False, False]),  # 25% verified
                location=random.choice(locations),
                category=categories[i % len(categories)],
                data_source="phyllo_mock"
            )
            creators.append(creator)
        
        return creators
    
    # ============ API REQUEST METHODS ============
    
    async def _get_access_token(self) -> str:
        """Get or refresh access token"""
        try:
            if self.config.use_mock:
                return "mock_phyllo_token"
            
            # Check if current token is valid
            if (self._access_token and self._token_expires_at and 
                datetime.utcnow() < self._token_expires_at - timedelta(minutes=5)):
                return self._access_token
            
            # Check cache
            cached_token = await self.redis.get("phyllo_access_token")
            if cached_token:
                token_data = json.loads(cached_token)
                self._access_token = token_data['access_token']
                self._token_expires_at = datetime.fromisoformat(token_data['expires_at'])
                return self._access_token
            
            # Request new token (implementation would go here)
            # For now, return mock token
            self._access_token = "phyllo_test_token"
            self._token_expires_at = datetime.utcnow() + timedelta(hours=24)
            return self._access_token
            
        except Exception as e:
            self.logger.error(f"Token retrieval failed: {str(e)}")
            return "fallback_token"
    
    async def _make_api_request(
        self, 
        endpoint: str, 
        method: str = "POST",
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make authenticated request to Phyllo API"""
        try:
            # Always try to call the real phyllo_dummy service first
            # Only use mock when the service is completely unavailable
            
            # Rate limiting
            await self._check_rate_limit()
            
            # Get access token
            access_token = await self._get_access_token()
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            # Ensure data is JSON serializable before sending
            if data:
                data = make_json_serializable(data)
            
            async with httpx.AsyncClient(verify=not self.config.disable_ssl) as client:
                if method.upper() == "POST":
                    response = await client.post(
                        f"{self.config.base_url}{endpoint}",
                        json=data,
                        headers=headers,
                        params=params,
                        timeout=30.0
                    )
                else:
                    response = await client.get(
                        f"{self.config.base_url}{endpoint}",
                        headers=headers,
                        params=params,
                        timeout=30.0
                    )
                
                await self._log_rate_limit_usage()
                
                if response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 60))
                    raise CreatorVerseError(f"Rate limit exceeded. Retry after {retry_after} seconds")
                
                if response.status_code not in [200, 201]:
                    raise CreatorVerseError(f"API request failed: {response.text}")
                
                return response.json()
                
        except httpx.TimeoutException:
            raise CreatorVerseError("API request timed out")
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            raise CreatorVerseError(f"API communication failed: {str(e)}")
    
    async def _check_rate_limit(self) -> None:
        """Check rate limits"""
        try:
            current_minute = int(datetime.utcnow().timestamp()) // 60
            rate_limit_key = f"phyllo_rate_limit:{current_minute}"
            
            current_count = await self.redis.get(rate_limit_key)
            current_count = int(current_count) if current_count else 0
            
            if current_count >= self.requests_per_minute:
                raise CreatorVerseError(f"Rate limit exceeded ({self.requests_per_minute} requests/minute)")
                
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            self.logger.warning(f"Rate limit check failed: {str(e)}")
    
    async def _log_rate_limit_usage(self) -> None:
        """Log rate limit usage"""
        try:
            current_minute = int(datetime.utcnow().timestamp()) // 60
            rate_limit_key = f"phyllo_rate_limit:{current_minute}"
            
            await self.redis.incr(rate_limit_key)
            await self.redis.expire(rate_limit_key, 120)
            
        except Exception as e:
            self.logger.warning(f"Rate limit logging failed: {str(e)}")


# Register the provider
from app.schemas.external_api.provider_interface import APIProviderFactory, APIProviderType

APIProviderFactory.register_provider(APIProviderType.PHYLLO, PhylloProvider)


class RedisClient:
    """Redis client implementation"""
    
    def __init__(self, redis_client=None, logger=None):
        self.redis_client = redis_client
        self.logger = logger
    
    @with_trace_id
    async def set(self, key: str, value: Any, expire: int = 0) -> bool:
        """Set a key-value pair in Redis"""
        if not self.redis_client:
            return False
        try:
            await self.redis_client.set(key, value, expire=expire)  # type: ignore
            return True
        except Exception as e:
            self.logger and self.logger.error(f"Redis set error: {e}")
            return False
    
    @with_trace_id
    async def get(self, key: str) -> Any:
        """Get a value by key from Redis"""
        if not self.redis_client:
            return None
        try:
            value = await self.redis_client.get(key)  # type: ignore
            return value
        except Exception as e:
            self.logger and self.logger.error(f"Redis get error: {e}")
            return None
    
    @with_trace_id
    async def delete(self, key: str) -> bool:
        """Delete a key from Redis"""
        if not self.redis_client:
            return False
        try:
            await self.redis_client.delete(key)  # type: ignore
            return True
        except Exception as e:
            self.logger and self.logger.error(f"Redis delete error: {e}")
            return False
    
    @with_trace_id
    async def exists(self, key: str) -> bool:
        """Check if a key exists in Redis"""
        if not self.redis_client:
            return False
        try:
            return await self.redis_client.exists(key)  # type: ignore
        except Exception as e:
            self.logger and self.logger.error(f"Redis exists error: {e}")
            return False
    
    @with_trace_id
    async def incr(self, key: str) -> int:
        """Increment a key by 1"""
        if not self.redis_client:
            return 0
        try:
            return await self.redis_client.incr(key)  # type: ignore
        except Exception as e:
            self.logger and self.logger.error(f"Redis incr error: {e}")
            return 0
    
    @with_trace_id
    async def expire(self, key: str, seconds: int) -> bool:
        """Set key expiration"""
        if not self.redis_client:
            return False
        try:
            return await self.redis_client.expire(key, seconds)  # type: ignore
        except Exception as e:
            self.logger and self.logger.error(f"Redis expire error: {e}")
            return False
    
    @with_trace_id
    async def delete_keys(self, *keys: str) -> int:
        """Delete multiple keys"""
        if not self.redis_client:
            return 0
        try:
            return await self.redis_client.delete(*keys)  # type: ignore
        except Exception as e:
            self.logger and self.logger.error(f"Redis delete_keys error: {e}")
            return 0
