"""
Pydantic schemas for creator discovery requests and responses
"""
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, validator

from app.schemas.filter_schemas import DiscoveryFilters


class SortOrder(str, Enum):
    """Sort order options"""
    ASC = "asc"
    DESC = "desc"


class SortField(str, Enum):
    """Available fields for sorting"""
    FOLLOWER_COUNT = "follower_count"
    ENGAGEMENT_RATE = "engagement_rate"
    CREDIBILITY_SCORE = "credibility_score"
    AVERAGE_LIKES = "average_likes"
    AVERAGE_VIEWS = "average_views"
    CREATED_AT = "created_at"
    UPDATED_AT = "updated_at"
    PLATFORM_USERNAME = "platform_username"
    FULL_NAME = "full_name"


class ViewMode(str, Enum):
    """Discovery view modes"""
    QUICK_VIEW = "quick"
    DETAILED_VIEW = "detailed"


class DataSource(str, Enum):
    """Data source preferences"""
    ALL = "all"
    INTERNAL_ONLY = "internal"
    EXTERNAL_ONLY = "external"
    PREFER_INTERNAL = "prefer_internal"
    PREFER_EXTERNAL = "prefer_external"


# Discovery Request Schemas
class SortCriteria(BaseModel):
    """Sort criteria for discovery results"""
    field: SortField = Field(..., description="Field to sort by")
    order: SortOrder = Field(SortOrder.DESC, description="Sort order")


class DiscoveryRequest(BaseModel):
    """Main discovery request schema"""
    filters: Optional[DiscoveryFilters] = Field(None, description="Filter criteria")
    sort_by: Optional[List[SortCriteria]] = Field(None, description="Sort criteria")
    view_mode: ViewMode = Field(ViewMode.QUICK_VIEW, description="View mode for results")
    data_source: DataSource = Field(DataSource.ALL, description="Data source preference")
    
    # Pagination
    page: int = Field(1, ge=1, description="Page number (1-based)")
    page_size: int = Field(20, ge=1, le=100, description="Number of results per page")
    
    # Search
    search_query: Optional[str] = Field(None, min_length=1, description="Text search query")
    search_fields: Optional[List[str]] = Field(None, description="Fields to search in")
    
    # Additional options
    include_external: bool = Field(True, description="Include external API results if needed")
    cache_preference: str = Field("balanced", description="Cache preference: strict, balanced, fresh")
    
    @validator('sort_by')
    def validate_sort_criteria(cls, v):
        if v and len(v) > 5:
            raise ValueError('Maximum 5 sort criteria allowed')
        return v


class SavedFilterDiscoveryRequest(BaseModel):
    """Discovery request using saved filter set"""
    saved_filter_id: str = Field(..., description="Saved filter set ID")
    sort_by: Optional[List[SortCriteria]] = Field(None, description="Sort criteria")
    view_mode: ViewMode = Field(ViewMode.QUICK_VIEW, description="View mode for results")
    
    # Pagination
    page: int = Field(1, ge=1, description="Page number (1-based)")
    page_size: int = Field(20, ge=1, le=100, description="Number of results per page")
    
    # Additional filters to add to saved set
    additional_filters: Optional[DiscoveryFilters] = Field(None, description="Additional filters to apply")


# Profile Response Schemas
class QuickViewProfile(BaseModel):
    """Quick view profile data for discovery results"""
    id: str = Field(..., description="Profile ID")
    platform_username: str = Field(..., description="Platform username")
    full_name: Optional[str] = Field(None, description="Full name")
    platform: str = Field(..., description="Social media platform")
    image_url: Optional[str] = Field(None, description="Profile image URL")
    
    # Key metrics for quick view
    follower_count: int = Field(..., description="Follower count")
    engagement_rate: float = Field(..., description="Engagement rate percentage")
    credibility_score: float = Field(..., description="Credibility score")
    
    # Status indicators
    is_verified: bool = Field(..., description="Verified account status")
    is_external: bool = Field(..., description="Whether this is an external creator")
    
    # Categorization
    follower_tier: str = Field(..., description="Follower tier (nano, micro, etc.)")
    engagement_tier: str = Field(..., description="Engagement tier (low, average, etc.)")
    primary_category: Optional[str] = Field(None, description="Primary content category")
    
    class Config:
        from_attributes = True


class DetailedProfile(BaseModel):
    """Detailed profile data for profile analytics view"""
    id: str = Field(..., description="Profile ID")
    work_platform_id: str = Field(..., description="Work platform ID")
    user_id: Optional[str] = Field(None, description="User ID if internal creator")
    
    # Platform information
    external_id: Optional[str] = Field(None, description="External platform ID")
    platform_username: str = Field(..., description="Platform username")
    platform: str = Field(..., description="Social media platform")
    url: Optional[str] = Field(None, description="Profile URL")
    image_url: Optional[str] = Field(None, description="Profile image URL")
    introduction: Optional[str] = Field(None, description="Bio/introduction")
    platform_account_type: Optional[str] = Field(None, description="Account type")
    
    # Demographics
    full_name: Optional[str] = Field(None, description="Full name")
    gender: Optional[str] = Field(None, description="Gender")
    age_group: Optional[str] = Field(None, description="Age group")
    language: Optional[str] = Field(None, description="Primary language")
    
    # Location
    country: Optional[str] = Field(None, description="Country code")
    state: Optional[str] = Field(None, description="State/Province")
    city: Optional[str] = Field(None, description="City")
    location_tier: Optional[str] = Field(None, description="Location tier")
    
    # Verification and status
    is_verified: bool = Field(..., description="Verified account status")
    profile_status: str = Field(..., description="Profile status")
    
    # Content metrics
    content_count: int = Field(..., description="Total content count")
    follower_count: int = Field(..., description="Follower count")
    following_count: int = Field(..., description="Following count")
    subscriber_count: int = Field(0, description="Subscriber count (YouTube)")
    
    # Engagement metrics
    average_likes: float = Field(..., description="Average likes per post")
    average_comments: float = Field(..., description="Average comments per post")
    average_views: float = Field(..., description="Average views per post")
    average_reels_views: float = Field(0, description="Average reels views")
    average_shares: float = Field(0, description="Average shares per post")
    
    # Calculated metrics
    engagement_rate: float = Field(..., description="Engagement rate percentage")
    credibility_score: float = Field(..., description="Credibility score")
    data_quality_score: float = Field(..., description="Data quality score")
    
    # Growth metrics
    follower_growth_rate: float = Field(0, description="Follower growth rate")
    follower_growth_30d: int = Field(0, description="30-day follower growth")
    follower_growth_90d: int = Field(0, description="90-day follower growth")
    
    # Content and interests
    primary_category: Optional[str] = Field(None, description="Primary content category")
    secondary_categories: Optional[List[str]] = Field(None, description="Secondary categories")
    interests: Optional[List[str]] = Field(None, description="Interest keywords")
    brand_affinity: Optional[List[str]] = Field(None, description="Brand affinity")
    
    # Audience data
    audience_age_groups: Optional[Dict[str, float]] = Field(None, description="Audience age distribution")
    audience_gender: Optional[Dict[str, float]] = Field(None, description="Audience gender distribution")
    audience_locations: Optional[Dict[str, float]] = Field(None, description="Audience location distribution")
    audience_interests: Optional[List[str]] = Field(None, description="Audience interests")
    
    # Metadata
    data_source: str = Field(..., description="Data source")
    is_external: bool = Field(..., description="Whether this is an external creator")
    last_updated_external: Optional[str] = Field(None, description="Last external update")
    cache_expires_at: Optional[str] = Field(None, description="Cache expiration time")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    
    # Computed properties
    follower_tier: str = Field(..., description="Follower tier category")
    engagement_tier: str = Field(..., description="Engagement tier category")
    
    class Config:
        from_attributes = True


# Discovery Response Schemas
class DiscoveryMeta(BaseModel):
    """Metadata for discovery response"""
    total_count: int = Field(..., description="Total number of matching profiles")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Results per page")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_previous: bool = Field(..., description="Whether there is a previous page")
    
    # Performance metadata
    query_time_ms: int = Field(..., description="Query execution time in milliseconds")
    cache_hit: bool = Field(..., description="Whether result was served from cache")
    external_api_calls: int = Field(0, description="Number of external API calls made")
    
    # Filter metadata
    filters_applied: Dict[str, Any] = Field(..., description="Summary of applied filters")
    sort_criteria: Optional[List[Dict[str, str]]] = Field(None, description="Applied sort criteria")


class QuickViewDiscoveryResponse(BaseModel):
    """Discovery response with quick view profiles"""
    profiles: List[QuickViewProfile] = Field(..., description="List of profiles")
    meta: DiscoveryMeta = Field(..., description="Response metadata")
    
    # Additional data for frontend
    filter_suggestions: Optional[Dict[str, List[str]]] = Field(None, description="Filter suggestions based on results")
    related_categories: Optional[List[str]] = Field(None, description="Related content categories")


class DetailedDiscoveryResponse(BaseModel):
    """Discovery response with detailed profiles"""
    profiles: List[DetailedProfile] = Field(..., description="List of detailed profiles")
    meta: DiscoveryMeta = Field(..., description="Response metadata")


# Single Profile Response Schemas
class ProfileAnalyticsRequest(BaseModel):
    """Request for detailed profile analytics"""
    profile_id: str = Field(..., description="Profile ID")
    include_audience: bool = Field(True, description="Include audience demographics")
    include_content_analysis: bool = Field(True, description="Include content analysis")
    refresh_external: bool = Field(False, description="Force refresh from external API")
    date_range: Optional[str] = Field(None, description="Date range for analytics (30d, 90d, 1y)")


class ProfileAnalyticsResponse(BaseModel):
    """Response for detailed profile analytics"""
    profile: DetailedProfile = Field(..., description="Detailed profile data")
    
    # Additional analytics data
    performance_trends: Optional[Dict[str, List[Dict[str, Any]]]] = Field(None, description="Performance trends over time")
    content_categories: Optional[Dict[str, int]] = Field(None, description="Content category distribution")
    best_performing_posts: Optional[List[Dict[str, Any]]] = Field(None, description="Best performing content")
    competitor_comparison: Optional[Dict[str, Any]] = Field(None, description="Comparison with similar creators")
    
    # Metadata
    data_freshness: str = Field(..., description="Data freshness indicator")
    last_external_sync: Optional[str] = Field(None, description="Last external API sync")
    confidence_score: float = Field(..., description="Data confidence score")


# Batch Operations
class BatchDiscoveryRequest(BaseModel):
    """Request for batch discovery with multiple filter sets"""
    filter_sets: List[DiscoveryFilters] = Field(..., min_items=1, max_items=10, description="Multiple filter sets")
    view_mode: ViewMode = Field(ViewMode.QUICK_VIEW, description="View mode for results")
    max_results_per_set: int = Field(50, ge=1, le=100, description="Max results per filter set")


class BatchDiscoveryResponse(BaseModel):
    """Response for batch discovery"""
    results: List[QuickViewDiscoveryResponse] = Field(..., description="Results for each filter set")
    aggregated_meta: Dict[str, Any] = Field(..., description="Aggregated metadata across all sets")


# Error Schemas
class DiscoveryError(BaseModel):
    """Error response for discovery operations"""
    error_code: str = Field(..., description="Error code")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")
