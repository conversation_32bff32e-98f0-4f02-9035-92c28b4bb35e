"""
Enhanced Creator Discovery API endpoints with two-mode system
"""
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from typing import Optional, List

from app.services.creator_discovery_service import EnhancedCreatorDiscoveryService
from app.services.phyllo_api_client import PhylloAPIClient
from app.schemas.discovery_schemas import (
    DiscoveryRequest, SavedFilterDiscoveryRequest, ProfileAnalyticsRequest,
    BatchDiscoveryRequest, ViewMode, DataSource, SortField, SortOrder
)
from app.schemas.filter_schemas import DiscoveryFilters, PlatformEnum
from app.utilities.response_handler import StandardResponse, StandardResponseModel, create_discovery_response
from app.core.exceptions import DiscoveryError, FilterValidationError, ProfileNotFoundError

router = APIRouter()
discovery_service = EnhancedCreatorDiscoveryService()
phyllo_client = PhylloAPIClient()


@router.post("/quick-search", response_model=StandardResponseModel[dict])
async def quick_search_creators(
    query: str = Body(..., description="Search query for creator discovery"),
    platform: PlatformEnum = Body(PlatformEnum.instagram, description="Platform to search"),
    limit: int = Body(10, description="Maximum number of results"),
    use_cache: bool = Body(True, description="Whether to use cached results")
):
    """
    Quick Search API - Fast creator discovery using Phyllo Quick Search
    
    This endpoint provides rapid creator discovery with basic information:
    - Fast response times (< 500ms)
    - Basic creator metrics
    - Suitable for search-as-you-type functionality
    - Limited to essential fields for performance
    """
    try:
        creators = await phyllo_client.quick_search(
            query=query,
            platform=platform,
            limit=limit,
            use_cache=use_cache
        )
        
        return StandardResponse.success(
            data={
                "creators": creators,
                "total_count": len(creators),
                "query": query,
                "platform": platform.value,
                "search_type": "quick_search"
            },
            message=f"Found {len(creators)} creators for '{query}'"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/advanced-search", response_model=StandardResponseModel[dict])
async def advanced_search_creators(
    filters: DiscoveryFilters = Body(..., description="Advanced filter criteria"),
    platform: PlatformEnum = Body(PlatformEnum.instagram, description="Platform to search"),
    page: int = Body(1, description="Page number"),
    page_size: int = Body(20, description="Results per page"),
    use_cache: bool = Body(True, description="Whether to use cached results")
):
    """
    Advanced Search API - Comprehensive creator discovery using Phyllo Advanced Search
    
    This endpoint provides detailed creator discovery with advanced filtering:
    - Complex filter combinations
    - Demographic and performance filters
    - Audience insights filtering
    - Content category filtering
    - Pagination support
    """
    try:
        creators, metadata = await phyllo_client.advanced_search(
            filters=filters,
            platform=platform,
            page=page,
            page_size=page_size,
            use_cache=use_cache
        )
        
        return StandardResponse.success(
            data={
                "creators": creators,
                "metadata": metadata,
                "filters_applied": filters.dict(),
                "platform": platform.value,
                "search_type": "advanced_search"
            },
            message=f"Found {len(creators)} creators with advanced filters"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/profile/{creator_id}/analytics", response_model=StandardResponseModel[dict])
async def get_creator_profile_analytics(
    creator_id: str,
    platform: PlatformEnum = Query(PlatformEnum.instagram, description="Creator's platform"),
    include_content: bool = Query(True, description="Include content analysis"),
    include_audience: bool = Query(True, description="Include audience demographics"),
    date_range: Optional[str] = Query(None, description="Date range (30d, 90d, 1y)"),
    refresh_external: bool = Query(False, description="Force refresh from external API")
):
    """
    Profile Analytics API - Comprehensive creator analytics using Phyllo Profile Analytics
    
    This endpoint provides detailed analytics for a specific creator:
    - Complete profile information
    - Performance metrics and trends
    - Audience demographics and insights
    - Content analysis and top performing posts
    - Historical reputation data
    - Brand collaboration insights
    """
    try:
        analytics_data = await phyllo_client.get_profile_analytics(
            creator_id=creator_id,
            platform=platform,
            include_content=include_content,
            include_audience=include_audience,
            date_range=date_range,
            use_cache=not refresh_external
        )
        
        return StandardResponse.success(
            data=analytics_data,
            message=f"Profile analytics retrieved for creator {creator_id}"
        )
        
    except ProfileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/discover", response_model=StandardResponseModel[dict])
async def discover_creators_enhanced(
    request: DiscoveryRequest = Body(..., description="Discovery request with filters and preferences")
):
    """
    Enhanced Discovery API - Two-mode creator discovery system
    
    This endpoint supports two discovery modes:
    
    **Quick View Mode** (view_mode: "quick_view"):
    - Fast search from cached database
    - Phyllo Quick Search API integration
    - Basic creator information
    - Optimized for browsing and initial discovery
    
    **Analytics Mode** (view_mode: "detailed"):
    - Phyllo Advanced Search API
    - Phyllo Profile Analytics API for detailed data
    - Comprehensive creator analytics
    - Suitable for detailed evaluation and decision making
    """
    try:
        profiles, metadata = await discovery_service.discover_creators(request)
        
        return create_discovery_response(
            profiles=profiles,
            metadata=metadata,
            request=request
        )
        
    except FilterValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except DiscoveryError as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch-analytics", response_model=StandardResponseModel[dict])
async def batch_profile_analytics(
    creator_ids: List[str] = Body(..., description="List of creator IDs"),
    platform: PlatformEnum = Body(PlatformEnum.instagram, description="Platform"),
    include_content: bool = Body(True, description="Include content analysis"),
    include_audience: bool = Body(True, description="Include audience insights")
):
    """
    Batch Profile Analytics - Get analytics for multiple creators
    
    This endpoint allows fetching detailed analytics for multiple creators in a single request:
    - Efficient batch processing
    - Parallel API calls to Phyllo
    - Error handling for individual failures
    - Suitable for campaign planning and creator comparison
    """
    try:
        results = []
        errors = []
        
        # Process creators in parallel (with reasonable concurrency limit)
        import asyncio
        semaphore = asyncio.Semaphore(5)  # Limit concurrent requests
        
        async def get_creator_analytics(creator_id: str):
            async with semaphore:
                try:
                    analytics = await phyllo_client.get_profile_analytics(
                        creator_id=creator_id,
                        platform=platform,
                        include_content=include_content,
                        include_audience=include_audience
                    )
                    return {"creator_id": creator_id, "analytics": analytics, "success": True}
                except Exception as e:
                    return {"creator_id": creator_id, "error": str(e), "success": False}
        
        # Execute batch requests
        tasks = [get_creator_analytics(creator_id) for creator_id in creator_ids]
        batch_results = await asyncio.gather(*tasks)
        
        # Separate successful results from errors
        for result in batch_results:
            if result["success"]:
                results.append(result)
            else:
                errors.append(result)
        
        return StandardResponse.success(
            data={
                "successful_results": results,
                "errors": errors,
                "total_requested": len(creator_ids),
                "successful_count": len(results),
                "error_count": len(errors)
            },
            message=f"Batch analytics completed: {len(results)} successful, {len(errors)} errors"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health", response_model=StandardResponseModel[dict])
async def discovery_health_check():
    """
    Health check endpoint for discovery service
    """
    try:
        # Test database connection
        async with discovery_service.db.get_db() as session:
            pass
        
        # Test Redis connection
        await discovery_service.redis_client.ping()
        
        # Test Phyllo API connection (optional)
        phyllo_status = "unknown"
        try:
            # This would be a lightweight health check endpoint if available
            phyllo_status = "healthy"
        except:
            phyllo_status = "unavailable"
        
        return StandardResponse.success(
            data={
                "database": "healthy",
                "redis": "healthy", 
                "phyllo_api": phyllo_status,
                "discovery_modes": ["quick_view", "analytics"],
                "supported_platforms": ["instagram", "youtube", "tiktok"]
            },
            message="Discovery service is healthy"
        )
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")


@router.get("/stats", response_model=StandardResponseModel[dict])
async def discovery_statistics():
    """
    Get discovery service statistics and metrics
    """
    try:
        # This would typically come from a metrics service
        stats = {
            "total_profiles_cached": 0,  # Would query database
            "cache_hit_rate": 0.85,
            "average_response_time_ms": 250,
            "phyllo_api_calls_today": 1250,
            "most_searched_platforms": ["instagram", "youtube", "tiktok"],
            "popular_filter_categories": ["demographic", "performance", "content"]
        }
        
        return StandardResponse.success(
            data=stats,
            message="Discovery statistics retrieved"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
