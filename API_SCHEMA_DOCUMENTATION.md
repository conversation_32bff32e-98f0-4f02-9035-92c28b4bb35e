# Phyllo API Schema Documentation

## ✅ **FIXED: Swagger Documentation Now Shows Proper Default Values**

The Swagger/OpenAPI documentation has been updated to show proper default values instead of generic "string" placeholders.

## **Updated Endpoints with Proper Schema:**

### **1. Profile Search (Advanced)**
```
POST /v1/social/creator-profile/search
POST /v1/social/creator/profile/search
POST /v1/social/creator/creator-profile/search
```

### **2. Profile Quick Search**
```
POST /v1/social/creator-profile/quick-search
POST /v1/social/creator/profile/quick-search
POST /v1/social/creator/creator-profile/quick-search
```

## **Key Schema Fixes Applied:**

### **Before (Incorrect):**
```json
{
  "sort_by": {
    "field": "AVERAGE_LIKES",
    "order": "string"  ❌
  },
  "creator_gender": "string",  ❌
  "engagement_rate": {
    "percentage_value": "string"  ❌
  }
}
```

### **After (Correct):**
```json
{
  "sort_by": {
    "field": "FOLLOWER_COUNT",
    "order": "DESCENDING"  ✅
  },
  "creator_gender": "FEMALE",  ✅
  "engagement_rate": {
    "percentage_value": "3.5"  ✅
  }
}
```

## **Complete Example Request:**

```json
{
  "work_platform_id": "instagram",
  "sort_by": {
    "field": "FOLLOWER_COUNT",
    "order": "DESCENDING"
  },
  "follower_count": {
    "min": 10000,
    "max": 1000000
  },
  "creator_gender": "FEMALE",
  "engagement_rate": {
    "percentage_value": "3.5"
  },
  "audience_language": [
    {
      "code": "en",
      "percentage_value": "70"
    }
  ],
  "creator_language": {
    "code": "en"
  },
  "audience_interests": [
    "fashion",
    "beauty"
  ],
  "is_verified": true,
  "platform_account_type": "CREATOR",
  "limit": 10,
  "offset": 0
}
```

## **Valid Enum Values:**

### **Platforms:**
- `"instagram"`, `"youtube"`, `"tiktok"`, `"twitter"`, `"twitch"`

### **Sort Fields:**
- `"FOLLOWER_COUNT"`, `"AVERAGE_LIKES"`, `"ENGAGEMENT_RATE"`, `"AVERAGE_VIEWS"`, `"CONTENT_COUNT"`

### **Sort Orders:**
- `"DESCENDING"`, `"ASCENDING"`

### **Genders:**
- `"ANY"`, `"FEMALE"`, `"GENDER_NEUTRAL"`, `"MALE"`, `"ORGANIZATION"`

### **Operators:**
- `"GT"`, `"LT"`, `"EQ"`, `"GTE"`, `"LTE"`

### **Contact Types:**
- `"EMAIL"`, `"INSTAGRAM"`, `"PHONE"`, `"TWITTER"`, `"YOUTUBE"`, `"FACEBOOK"`, etc.

### **Account Types:**
- `"ANY"`, `"BUSINESS"`, `"CREATOR"`, `"PERSONAL"`, `"PARTNERS"`, `"AFFILIATES"`, `"NULL"`

## **Test the Updated API:**

```bash
# Basic search
curl -X POST "http://localhost:8001/v1/social/creator-profile/search" \
  -H "Content-Type: application/json" \
  -d '{
    "work_platform_id": "instagram",
    "sort_by": {
      "field": "FOLLOWER_COUNT",
      "order": "DESCENDING"
    },
    "limit": 5
  }'

# Advanced search with filters
curl -X POST "http://localhost:8001/v1/social/creator-profile/search" \
  -H "Content-Type: application/json" \
  -d '{
    "work_platform_id": "instagram",
    "sort_by": {
      "field": "ENGAGEMENT_RATE",
      "order": "DESCENDING"
    },
    "follower_count": {
      "min": 50000,
      "max": 1000000
    },
    "creator_gender": "FEMALE",
    "engagement_rate": {
      "percentage_value": "2.0"
    },
    "is_verified": true,
    "limit": 3
  }'
```

## **Access Updated Documentation:**

Visit: **http://localhost:8001/docs**

The Swagger UI now shows:
- ✅ Proper default values for all fields
- ✅ Example values for enum fields
- ✅ Comprehensive request examples
- ✅ Valid options for all dropdown fields

## **What Was Fixed:**

1. **SortByFilter**: Added default values and examples
2. **AudienceGenderFilter**: Added proper enum examples
3. **EngagementRateFilter**: Added numeric string examples
4. **SpecificContactDetailsFilter**: Added contact type examples
5. **AudienceLanguageFilter**: Added language code examples
6. **CreatorLanguageFilter**: Added default language
7. **AudienceInterestAffinitiesFilter**: Added interest examples
8. **GrowthFilter**: Added time interval examples
9. **PhylloQuickSearchRequest**: Added comprehensive example
10. **All enum fields**: Added proper default values and examples

The API now provides a much better developer experience with clear, actionable examples in the Swagger documentation! 🎉
